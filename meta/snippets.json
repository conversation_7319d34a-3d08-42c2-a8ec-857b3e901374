[
  {
    "title": "产品详情-常见问题",
    "screenshot": "https://img.alicdn.com/imgextra/i2/O1CN01yGauiD1MGujDwNzVk_!!6000000001408-2-tps-1413-392.png",
    "snippet": {
      "props": {
        "categories": [
          {
            "name": "基础问题",
            "questions": [
              {
                "question": "什么是阿里云？",
                "answer": "阿里云是阿里巴巴集团旗下的云计算服务平台，为全球客户提供弹性计算、数据库、存储与CDN、网络、安全、大数据、人工智能等云计算服务。",
                "detailUrl": "https://help.aliyun.com/zh/ecs/getting-started/what-is-ecs"
              },
              {
                "question": "如何注册阿里云账号？",
                "answer": "您可以访问阿里云官网，点击右上角的"免费注册"按钮，按照页面提示填写手机号码、邮箱等信息完成注册。注册成功后，建议您完成实名认证以享受更多服务。",
                "detailUrl": "https://help.aliyun.com/zh/account-management/user-guide/sign-up-with-alibaba-cloud"
              },
              {
                "question": "阿里云有哪些主要产品？",
                "answer": "阿里云主要产品包括：弹性计算（ECS、容器服务等）、数据库（RDS、MongoDB等）、存储与CDN、网络产品、安全产品、大数据产品、人工智能产品等。",
                "detailUrl": "https://www.aliyun.com/product"
              }
            ]
          },
          {
            "name": "高级功能",
            "questions": [
              {
                "question": "如何配置负载均衡？",
                "answer": "在阿里云控制台中，选择负载均衡产品，创建负载均衡实例，配置监听器和后端服务器组，设置健康检查规则，即可实现流量的智能分发。",
                "detailUrl": "https://help.aliyun.com/zh/slb/application-load-balancer/user-guide/getting-started-with-application-load-balancer"
              },
              {
                "question": "如何设置自动伸缩？",
                "answer": "通过弹性伸缩服务（Auto Scaling），您可以根据业务需求和策略自动调整计算资源。创建伸缩组，配置伸缩规则和策略，系统会自动增减ECS实例。",
                "detailUrl": "https://help.aliyun.com/zh/auto-scaling/getting-started/getting-started"
              },
              {
                "question": "如何使用容器服务？",
                "answer": "阿里云容器服务ACK提供高性能可伸缩的容器应用管理能力。您可以创建Kubernetes集群，部署容器化应用，实现应用的快速部署和管理。",
                "detailUrl": "https://help.aliyun.com/zh/ack/ack-managed-and-ack-dedicated/getting-started/getting-started-1"
              }
            ]
          },
          {
            "name": "故障排查",
            "questions": [
              {
                "question": "ECS实例无法连接怎么办？",
                "answer": "请检查安全组规则是否正确配置，确认22端口（Linux）或3389端口（Windows）已开放。同时检查实例状态、网络配置和防火墙设置。",
                "detailUrl": "https://help.aliyun.com/zh/ecs/user-guide/troubleshoot-logon-issues"
              },
              {
                "question": "网站访问速度慢如何优化？",
                "answer": "可以通过以下方式优化：使用CDN加速静态资源、优化数据库查询、启用Gzip压缩、使用负载均衡分散流量、选择合适的实例规格等。",
                "detailUrl": "https://help.aliyun.com/zh/cdn/user-guide/overview-13"
              },
              {
                "question": "数据库连接超时怎么解决？",
                "answer": "检查数据库实例状态、网络连通性、连接数限制、安全组配置。可以调整连接超时参数、优化SQL查询、增加连接池大小等方式解决。",
                "detailUrl": "https://help.aliyun.com/zh/rds/apsaradb-rds-for-mysql/troubleshoot-the-timeout-error-that-occurs-when-you-connect-to-an-apsaradb-rds-for-mysql-instance"
              }
            ]
          }
        ],
        "jumpCard": {
          "title": "您的问题得不到解答？来了解更多常见问题，找到您的答案",
          "url": "https://help.aliyun.com/"
        }
      }
    }
  }
]