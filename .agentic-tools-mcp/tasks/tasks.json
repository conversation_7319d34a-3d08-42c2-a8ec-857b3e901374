{"projects": [{"id": "************************************", "name": "FAQ List Blueprint Development", "description": "基于物料开发指引，完成常见问题列表组件的开发，包括项目初始化、需求理解、schema生成、代码实现、文档完善等完整流程", "createdAt": "2025-07-02T08:12:42.564Z", "updatedAt": "2025-07-02T08:12:42.564Z"}], "tasks": [{"id": "7ab86ccb-2819-4340-a345-67a558da5bdb", "name": "1.1 读取项目元信息", "details": "阅读 `02-project-setup/metadata.json` 获取项目基本信息，理解项目名称、npm包名、作者、版本等信息。验证项目信息是否完整和正确。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:13:19.767Z", "updatedAt": "2025-07-02T08:24:10.309Z", "dependsOn": [], "priority": 10, "complexity": 2, "status": "done", "tags": ["项目初始化", "元信息"], "estimatedHours": 0.5, "level": 0}, {"id": "3babe351-33a5-4bab-92ae-5f890cf1921a", "name": "1.2 执行项目初始化", "details": "根据 `02-project-setup/initialization-guide.md` 在当前项目下执行初始化。解压 `02-project-setup/template.zip` 到项目根目录，安装依赖：执行命令 `tnpm install`。确保所有依赖正确安装。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:13:31.743Z", "updatedAt": "2025-07-02T08:25:24.978Z", "dependsOn": ["7ab86ccb-2819-4340-a345-67a558da5bdb"], "priority": 9, "complexity": 3, "status": "done", "tags": ["项目初始化", "环境搭建"], "estimatedHours": 1, "level": 0}, {"id": "47a0970f-3061-4dfe-a5b0-8313f896d211", "name": "1.3 理解项目结构", "details": "学习 `02-project-setup/architecture-spec.md` 了解目录结构规范。验证项目目录结构是否符合规范要求，包括src、meta、blueprints等目录的正确性。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:13:43.070Z", "updatedAt": "2025-07-02T08:25:50.839Z", "dependsOn": ["3babe351-33a5-4bab-92ae-5f890cf1921a"], "priority": 8, "complexity": 2, "status": "done", "tags": ["项目初始化", "架构理解"], "estimatedHours": 0.5, "level": 0}, {"id": "67f26628-6ea6-4b28-ac1d-f04486070593", "name": "2.1 阅读原始需求", "details": "仔细阅读 `03-requirements/requirements-spec.md`，理解功能描述、数据结构、UI交互细节。重点理解：1）标签页展示分类列表功能；2）每个分类下的问答对列表；3）跳转类卡片功能；4）响应式布局要求。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:13:56.391Z", "updatedAt": "2025-07-02T08:26:06.572Z", "dependsOn": ["47a0970f-3061-4dfe-a5b0-8313f896d211"], "priority": 9, "complexity": 3, "status": "done", "tags": ["需求理解", "功能分析"], "estimatedHours": 1, "level": 0}, {"id": "601ac392-5f3f-4819-9393-885e348c5511", "name": "2.2 更新项目描述", "details": "根据需求更新 `package.json` 中的 `description` 字段，更新 `README.md` 中的项目名称和描述部分。确保项目描述准确反映常见问题列表组件的功能。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:14:08.158Z", "updatedAt": "2025-07-02T08:26:38.811Z", "dependsOn": ["67f26628-6ea6-4b28-ac1d-f04486070593"], "priority": 7, "complexity": 2, "status": "done", "tags": ["需求理解", "文档更新"], "estimatedHours": 0.5, "level": 0}, {"id": "bf2a2836-0aa6-42c5-89c6-d7d9eda983c4", "name": "3.1 学习Schema规范", "details": "阅读 `01-knowledge-base/schema-spec.md` 了解schema编写规范。重点理解：1）基础数据类型配置；2）模型设置器的使用方法；3）x-format和x-format-options的正确配置方式；4）信息中心字段映射规则。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:14:19.035Z", "updatedAt": "2025-07-02T08:27:19.506Z", "dependsOn": ["601ac392-5f3f-4819-9393-885e348c5511"], "priority": 9, "complexity": 4, "status": "done", "tags": ["schema开发", "规范学习"], "estimatedHours": 1.5, "level": 0}, {"id": "23644fec-2378-4edc-ab97-a9b8ae53f906", "name": "3.2 理解信息中心数据模型", "details": "参考需求中提供的常见问题信息中心模型结构，理解字段含义：y3nnz98yp4ogg473q94g(提问)、8kwg6lm9hulr4ja5ao8i(回答)、ac9fnxmq3vscu515i1qu(详情页地址)、tc6z6bsif0r1ai8lmgba(所属分类)、fuzz12k8bj1tz0y7scn0(产品code)。分析数据实例，理解数据结构。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:14:35.050Z", "updatedAt": "2025-07-02T08:27:52.725Z", "dependsOn": ["bf2a2836-0aa6-42c5-89c6-d7d9eda983c4"], "priority": 8, "complexity": 3, "status": "done", "tags": ["schema开发", "数据模型"], "estimatedHours": 1, "level": 0}, {"id": "6070a9c5-b8e1-400a-a5fe-5599fb7c109d", "name": "3.3 编写schema.json", "details": "在 `meta/` 目录下创建 `schema.json`，根据需求配置数据结构：1）常见问题分类列表（支持3-5个分类）；2）每个分类下的问答对列表；3）跳转卡片的标题和链接配置。正确配置模型设置器的 `x-format` 和 `x-format-options`，确保与 `type: \"array\"` 在同一层级，建立schema字段与信息中心字段的正确映射关系。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:14:48.607Z", "updatedAt": "2025-07-02T08:29:10.553Z", "dependsOn": ["23644fec-2378-4edc-ab97-a9b8ae53f906"], "priority": 9, "complexity": 7, "status": "done", "tags": ["schema开发", "数据结构"], "estimatedHours": 3, "level": 0}, {"id": "96d7c6a5-fccb-4f40-9680-11add9c34778", "name": "4.1 生成Mock数据结构", "details": "根据上一步生成的 `schema.json` 数据格式，参考需求中提供的信息中心数据示例，在 `meta/` 目录下创建 `mock.json`。确保数据内容包含：至少3个问题分类，每个分类至少3个问答对，跳转卡片包含标题和链接。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:15:02.020Z", "updatedAt": "2025-07-02T08:30:42.789Z", "dependsOn": ["6070a9c5-b8e1-400a-a5fe-5599fb7c109d"], "priority": 8, "complexity": 5, "status": "done", "tags": ["mock数据", "数据生成"], "estimatedHours": 2, "level": 0}, {"id": "e2c486dc-dbdf-427f-bb7f-3ae20ff33b1a", "name": "4.2 数据一致性验证", "details": "确保 mock.json 中的所有字段与schema.json中定义的字段完全匹配，验证数据类型和格式符合schema约束。检查字段完整性，确保包含schema中定义的所有字段，数据类型正确。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:15:13.532Z", "updatedAt": "2025-07-02T08:31:05.086Z", "dependsOn": ["96d7c6a5-fccb-4f40-9680-11add9c34778"], "priority": 7, "complexity": 3, "status": "done", "tags": ["mock数据", "数据验证"], "estimatedHours": 1, "level": 0}, {"id": "22db2146-b3f6-4c8d-b216-0edc76a7e3f1", "name": "5.1 学习Snippets规范", "details": "阅读 `01-knowledge-base/snippets-spec.md` 了解snippets编写规范。理解snippets.json的结构要求：title字段、screenshot字段、snippet.props字段的配置方法。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:15:24.625Z", "updatedAt": "2025-07-02T08:31:24.499Z", "dependsOn": ["e2c486dc-dbdf-427f-bb7f-3ae20ff33b1a"], "priority": 7, "complexity": 2, "status": "done", "tags": ["snippets开发", "规范学习"], "estimatedHours": 0.5, "level": 0}, {"id": "1060d15e-278f-4c80-a313-2bec3518ee41", "name": "5.2 生成snippets.json", "details": "在 `meta/` 目录下创建 `snippets.json`，基于 schema.json 和 mock.json 生成默认配置。更新 screenshots 图片地址为元信息中的预览图地址。确保数据结构与 schema.json 完全一致，验证默认数据的有效性。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:15:36.185Z", "updatedAt": "2025-07-02T08:32:30.913Z", "dependsOn": ["22db2146-b3f6-4c8d-b216-0edc76a7e3f1"], "priority": 7, "complexity": 4, "status": "done", "tags": ["snippets开发", "配置生成"], "estimatedHours": 1.5, "level": 0}, {"id": "b5f05396-f675-435b-9dfc-ef0ead8e043b", "name": "6.1 学习基础知识", "details": "学习开发基础知识：1）学习 `01-knowledge-base/foundation-guide.md` - 项目基础；2）学习 `01-knowledge-base/best-practices.md` - 最佳实践；3）学习 `01-knowledge-base/com-design-guide.md` - 基础组件使用指导。理解COM Design组件库的使用方法，掌握响应式布局原则，了解组件开发规范。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:15:48.291Z", "updatedAt": "2025-07-02T08:32:53.688Z", "dependsOn": ["1060d15e-278f-4c80-a313-2bec3518ee41"], "priority": 8, "complexity": 4, "status": "done", "tags": ["组件开发", "基础学习"], "estimatedHours": 2, "level": 0}, {"id": "05acfa9a-1079-452f-b712-8815c5eac5a9", "name": "6.2 理解设计稿和JSX结构", "details": "结合原始需求、metadata.json、schema.json理解数据结构。按照 `04-design/jsx-structure.md` 结构，参考 `04-design/api-reference/index.md` 的组件API指引，理解JSX结构中的元素层次和组件关系。注意JSX结构的置信度更高，当JSX结构和需求有冲突时，以JSX结构为准。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:16:01.232Z", "updatedAt": "2025-07-02T08:33:11.637Z", "dependsOn": ["b5f05396-f675-435b-9dfc-ef0ead8e043b"], "priority": 9, "complexity": 5, "status": "done", "tags": ["组件开发", "设计理解"], "estimatedHours": 2, "level": 0}, {"id": "f0bbad86-6a17-4c97-886f-6c2b11123753", "name": "6.3 实现基础UI结构", "details": "在 `src/index.tsx` 中按照 JSX 结构还原设计稿。实现Container+Flex的响应式布局，左侧Tabs组件展示问答列表，右侧Box组件展示跳转卡片。注意除了布局类组件可以微调外，JSX 结构中的元素不要修改。确保所有JSX结构中的元素都在UI中正确对应。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:16:15.004Z", "updatedAt": "2025-07-02T08:34:35.235Z", "dependsOn": ["05acfa9a-1079-452f-b712-8815c5eac5a9"], "priority": 9, "complexity": 6, "status": "done", "tags": ["组件开发", "UI实现"], "estimatedHours": 3, "level": 0}, {"id": "c9e23d65-37d3-4632-9658-a4361e559fc4", "name": "6.4 处理图标实现", "details": "严格对照 `04-design/jsx-structure.md` 中的图标使用，确保所有图标都在实现中正确对应。使用 `01-knowledge-base/icon-list.csv` 确定图标的 `iconType` 字段，根据iconType选择正确的npm包（global: @ali/adc-icons, biz: @ali/adc-biz-icons, product: @ali/adc-product-icons）。参考 `01-knowledge-base/icon-guide.md` 调整图标代码，基于 `dsl.json` 中的尺寸描述为图标添加 `font-size` 样式。确保所有使用的图标都已正确导入对应的npm包。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:16:29.580Z", "updatedAt": "2025-07-02T08:37:30.308Z", "dependsOn": ["f0bbad86-6a17-4c97-886f-6c2b11123753"], "priority": 8, "complexity": 5, "status": "done", "tags": ["组件开发", "图标处理"], "estimatedHours": 2, "level": 0}, {"id": "5ad5d54d-7734-4a54-a2f4-4ab40a4f8222", "name": "6.5 UI细节优化", "details": "下载项目元数据中的预览图片作为参考，对比预览图调整UI细节（文本颜色、文本尺寸、间距等）。保持整体布局不变，仅做细节微调，检查：1）容器的垂直对齐方向是否和预览图一致；2）容器的水平对齐方向是否和预览图一致；3）文本样式和间距是否符合设计要求。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:16:41.263Z", "updatedAt": "2025-07-02T08:38:43.021Z", "dependsOn": ["c9e23d65-37d3-4632-9658-a4361e559fc4"], "priority": 7, "complexity": 4, "status": "done", "tags": ["组件开发", "UI优化"], "estimatedHours": 2, "level": 0}, {"id": "ebee1405-f291-4e43-a816-735473fd6eaa", "name": "6.6 组件拆分", "details": "如果 `src/index.tsx` 超过200行，按单一职责原则拆分。在 `/src/components/` 创建子组件文件夹，将相关逻辑拆分到子组件中。建议拆分为：标签页组件（TabsSection）、卡片组件（CardSection）等。确保代码结构清晰，组件职责单一。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:16:51.441Z", "updatedAt": "2025-07-02T08:39:03.625Z", "dependsOn": ["5ad5d54d-7734-4a54-a2f4-4ab40a4f8222"], "priority": 6, "complexity": 4, "status": "done", "tags": ["组件开发", "代码重构"], "estimatedHours": 1.5, "level": 0}, {"id": "9658d20a-9b8b-4947-a243-5fd0ee2ed6f8", "name": "6.7 类型声明定义", "details": "在 `/src/types/index.ts` 定义组件Props类型，确保类型与 schema.json 数据结构完全一致。如果类型较多，可拆分成多个类型文件。定义常见问题分类、问答对、跳转卡片等相关的TypeScript接口。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:17:01.197Z", "updatedAt": "2025-07-02T08:40:41.009Z", "dependsOn": ["ebee1405-f291-4e43-a816-735473fd6eaa"], "priority": 8, "complexity": 3, "status": "done", "tags": ["组件开发", "类型定义"], "estimatedHours": 1, "level": 0}, {"id": "6af272e6-4810-4adb-b34d-3cb0eb663f67", "name": "6.8 数据逻辑填充", "details": "将UI代码中的静态内容替换为props数据，确保所有数据都来自props，不直接引入mock数据。如果是数组类数据，每次遍历时注意考虑其可能为 undefined 或者为空数组的情况，避免 js 运行报错。示例：`<div>{list?.length > 0 ? list.map() : null}</div>`。确保UI使用props数据而非静态内容。", "projectId": "************************************", "completed": true, "createdAt": "2025-07-02T08:17:11.923Z", "updatedAt": "2025-07-02T08:41:36.797Z", "dependsOn": ["9658d20a-9b8b-4947-a243-5fd0ee2ed6f8"], "priority": 9, "complexity": 5, "status": "done", "tags": ["组件开发", "数据绑定"], "estimatedHours": 2, "level": 0}, {"id": "4c338eac-b462-4304-9a0a-9bfc638d991b", "name": "6.9 完善交互逻辑", "details": "根据需求内容，完善相关元素的点击、hover等交互效果。实现：1）卡片hover阴影过渡效果；2）问答对的跳转链接点击功能；3）标签页切换功能；4）响应式布局交互。确保所有交互符合设计要求。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:17:21.685Z", "updatedAt": "2025-07-02T08:17:21.685Z", "dependsOn": ["6af272e6-4810-4adb-b34d-3cb0eb663f67"], "priority": 7, "complexity": 4, "status": "pending", "tags": ["组件开发", "交互实现"], "estimatedHours": 1.5, "level": 0}, {"id": "51926699-25dd-4248-92bc-2dd4975a0414", "name": "6.10 代码质量检查", "details": "执行代码质量检查：1）执行prettier格式化；2）执行 `npm run format` 进行代码格式化；3）执行 `npm run lint` 进行代码检查；4）检查并移除未使用的变量和导入；5）执行 `npm run dev` 确认调试页面正常运行。确保无lint错误，无未使用变量，开发服务器可正常启动，页面渲染正常。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:07.287Z", "updatedAt": "2025-07-02T08:18:07.287Z", "dependsOn": ["4c338eac-b462-4304-9a0a-9bfc638d991b"], "priority": 8, "complexity": 3, "status": "pending", "tags": ["组件开发", "代码质量"], "estimatedHours": 1, "level": 0}, {"id": "08197aac-5505-4841-8ed1-94978aacc813", "name": "7.1 完善README文档", "details": "按照 `01-knowledge-base/doc-standards.md` 完善README文档。包含：1）一级标题使用npm包名；2）基于需求简要描述物料功能；3）添加元信息中的预览图，宽度固定为600px；4）创建markdown表格展示schema.json字段（字段名、中文描述、类型、默认值、数据来源）；5）添加项目安装和调试命令；6）列出相关链接（设计稿、需求稿地址、git仓库地址等）。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:20.050Z", "updatedAt": "2025-07-02T08:18:20.050Z", "dependsOn": ["51926699-25dd-4248-92bc-2dd4975a0414"], "priority": 7, "complexity": 4, "status": "pending", "tags": ["文档完善", "README"], "estimatedHours": 2, "level": 0}, {"id": "efa8266e-30ae-4021-b8f2-78c5054ece85", "name": "7.2 更新CHANGELOG", "details": "在 `CHANGELOG.md` 中添加当前版本的功能点，基于需求描述编写变更日志。按照版本号倒序编写，最新的版本号在前面。格式示例：`## v0.0.1 - FEAT: 初始化常见问题列表组件，支持标签页展示分类和问答对列表，支持跳转卡片功能 (@lianmin.slm)`。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:28.822Z", "updatedAt": "2025-07-02T08:18:28.822Z", "dependsOn": ["08197aac-5505-4841-8ed1-94978aacc813"], "priority": 6, "complexity": 2, "status": "pending", "tags": ["文档完善", "CHANGELOG"], "estimatedHours": 0.5, "level": 0}, {"id": "e9cc84a1-c642-4a69-9783-8fc0ed32c547", "name": "8.1 项目结构检查", "details": "检查项目目录结构是否符合 `02-project-setup/architecture-spec.md` 规范，调整不符合规范的文件或目录。验证：1）目录结构符合规范；2）文件命名正确；3）组件结构合理。确保所有文件都在正确的位置。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:37.392Z", "updatedAt": "2025-07-02T08:18:37.392Z", "dependsOn": ["efa8266e-30ae-4021-b8f2-78c5054ece85"], "priority": 7, "complexity": 2, "status": "pending", "tags": ["全局反思", "项目结构"], "estimatedHours": 0.5, "level": 0}, {"id": "9b65c771-30d0-4062-a7f0-c485bbe97569", "name": "8.2 代码质量最终检查", "details": "执行最终的代码质量检查：1）Linter检查：修正所有类型和属性用法错误；2）样式引入：确保 `src/index.tsx` 中引入 `@ali/adc/es/index.css`；3）Props一致性：确保 `src/index.tsx` 的props类型与 mock/schema/snippets 完全一致；4）引入检查：检查所有 `*.tsx` 文件的引入是否正确；5）扩展名检查：检查所有的 `*.tsx`，如果文件里未使用jsx代码，应该统一成 `*.ts` 和 `*.js`；6）图标规范检查：验证图标与设计稿的一致性，确保图标npm包选择正确，检查图标导入声明的完整性，验证图标尺寸样式符合dsl.json要求；7）数据结构一致性：验证所有meta文件（schema/mock/snippets）的数据结构完全一致；8）JSON格式验证：使用 `JSON.parse()` 验证所有JSON文件格式正确。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:51.220Z", "updatedAt": "2025-07-02T08:18:51.220Z", "dependsOn": ["e9cc84a1-c642-4a69-9783-8fc0ed32c547"], "priority": 9, "complexity": 5, "status": "pending", "tags": ["全局反思", "代码质量"], "estimatedHours": 2, "level": 0}, {"id": "8065486d-9ba6-40b0-b6f9-b0cd50e2a627", "name": "8.3 文档完整性检查", "details": "检查 README.md 和 CHANGELOG.md 是否完整，确保所有必要信息都已包含。验证：1）文档内容完整；2）信息准确无误；3）格式规范；4）链接有效。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:18:59.046Z", "updatedAt": "2025-07-02T08:18:59.046Z", "dependsOn": ["9b65c771-30d0-4062-a7f0-c485bbe97569"], "priority": 6, "complexity": 2, "status": "pending", "tags": ["全局反思", "文档检查"], "estimatedHours": 0.5, "level": 0}, {"id": "b81eda53-7c7c-4480-8ab2-72cd54f895a3", "name": "8.4 最终构建验证", "details": "执行最终构建验证，确保项目可以正常构建和运行：1）执行 `npm run build:npm` 确认构建成功无错误；2）执行 `npm run dev` 确认开发服务器正常启动；3）在浏览器中验证页面渲染正常；4）测试响应式布局在不同屏幕尺寸下的表现；5）验证所有交互功能正常工作。", "projectId": "************************************", "completed": false, "createdAt": "2025-07-02T08:19:08.107Z", "updatedAt": "2025-07-02T08:19:08.107Z", "dependsOn": ["8065486d-9ba6-40b0-b6f9-b0cd50e2a627"], "priority": 9, "complexity": 3, "status": "pending", "tags": ["全局反思", "构建验证"], "estimatedHours": 1, "level": 0}, {"id": "32473998-fdf1-4efb-8043-8a70aaaffc52", "name": "配置常见问题分类数据结构", "details": "在schema.json中配置常见问题分类的数据结构，支持3-5个分类。每个分类包含分类名称和问题列表。定义分类的基础字段：分类ID、分类名称、问题列表等。", "projectId": "************************************", "parentId": "6070a9c5-b8e1-400a-a5fe-5599fb7c109d", "completed": false, "createdAt": "2025-07-02T08:19:17.280Z", "updatedAt": "2025-07-02T08:19:17.280Z", "priority": 5, "complexity": 3, "status": "pending"}, {"id": "004ecb78-7d92-4771-9899-f67188e3ec20", "name": "配置问答对数据结构", "details": "在schema.json中配置每个分类下的问答对列表数据结构。每个问答对包含：问题、答案、跳转链接。设置合理的字段约束和默认值。", "projectId": "************************************", "parentId": "6070a9c5-b8e1-400a-a5fe-5599fb7c109d", "completed": false, "createdAt": "2025-07-02T08:19:23.580Z", "updatedAt": "2025-07-02T08:19:23.580Z", "priority": 5, "complexity": 3, "status": "pending"}, {"id": "3ddcbde8-9354-4af5-8ac4-20a1500d8475", "name": "配置模型设置器映射", "details": "正确配置模型设置器的 x-format 和 x-format-options，确保与 type: \"array\" 在同一层级。建立schema字段与信息中心字段的正确映射关系：y3nnz98yp4ogg473q94g(提问)、8kwg6lm9hulr4ja5ao8i(回答)、ac9fnxmq3vscu515i1qu(详情页地址)等。", "projectId": "************************************", "parentId": "6070a9c5-b8e1-400a-a5fe-5599fb7c109d", "completed": false, "createdAt": "2025-07-02T08:19:32.448Z", "updatedAt": "2025-07-02T08:19:32.448Z", "priority": 5, "complexity": 3, "status": "pending"}, {"id": "24672d79-04a1-402c-aa44-701d966caf2f", "name": "配置跳转卡片数据结构", "details": "在schema.json中配置跳转卡片的数据结构，包含标题和跳转链接两个字段。设置合理的字段类型、长度限制和默认值。", "projectId": "************************************", "parentId": "6070a9c5-b8e1-400a-a5fe-5599fb7c109d", "completed": false, "createdAt": "2025-07-02T08:19:38.144Z", "updatedAt": "2025-07-02T08:19:38.144Z", "priority": 5, "complexity": 3, "status": "pending"}], "subtasks": [], "migration": {"version": "1.8.0", "migratedAt": "2025-07-02T08:22:36.414Z", "subtasksMigrated": 1}}