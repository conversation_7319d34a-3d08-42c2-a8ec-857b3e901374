物料开发相关规范

# 项目开发规范

## 项目结构与规范指南

非迭代场景下，简单需求无需新建文件，直接在主入口文件 `src/index.tsx` 中进行修改即可。

### 文件命名规范

- 组件文件：对于一个文件夹里的根组件，应该用 `index.tsx` 作为文件名，同时用文件夹名作为组件名并使用 PascalCase 命名法
- 工具文件：使用 camelCase 命名法，例如 `utils.ts`
- 样式文件：对于一个文件夹里的样式文件，采用`index.module.css` 或 `index.less`作为扩展名
- 类型文件：使用`.d.ts`作为文件后缀
- 常量文件：全小写命名，如 `constant.ts`

### 二、编码规范

#### 1. TypeScript 使用规则

- 必须全程使用 TypeScript 进行开发。
- 遵循项目根目录下 `tsconfig.json` 文件内的配置项。
- 所有组件都需明确声明其 `props` 类型。
- 避免使用 `any` 类型；若确实需要，请在注释中说明理由。

#### 组件开发指导原则

- 推荐使用函数式组件配合 Hooks 模式编写。
- 新增组件应当放置于`src/components/`路径下。
- 单个组件文件长度不应超过 500 行代码；超出此限制时需要拆分组件
- 每个独立组件建议遵循如下目录结构，举例：

```plaintext
└── src/                # 源代码目录
    └── components/     # 组件目录
        └── BannerCard/         # 独立组件文件夹，名字自定义
            ├── index.tsx/       # BannerCard 组件内容
            └── index.module.less/       # BannerCard 组件样式
```

- 每次生码完成之后一定要更新元数据：
  - 根据 props 接口生成键值对 json 文件并更新 `meta\mock.json`
  - 获取 schema.json 的规范并根据 `meta\mock.json` 更新 `meta\schema.json`
  - 获取 snippets.json 的规范并根据 `meta\schema.json` 更新 `meta\snippets.json`

### UI 组件使用规范

- **优先选择**：@ali/adc 组件库，参考 `03-design/api-reference/` 目录下的组件文档。 如果文档缺失，可以从 `/node_modules/@ali/adc/` 目录下检索注释和 TypeScript 的类型。
- **禁止使用**：Ant Design 或 Fusion 组件。
- **当@ali/adc 无合适组件时**：使用原生 HTML 元素作为替代方案。

### 样式规范

- **样式文件格式**：推荐使用 CSS Modules（.module.css）来管理样式。
- **避免全局样式污染**：尽量不要使用全局样式覆盖局部样式。
- **内联样式的限制**：禁止在项目中使用内联样式，例如`style={{
  padding: '20px',
  border: '1px solid #8E8E8E',
  textAlign: 'left',
}}`
- **重要声明的谨慎使用**：仅在绝对必要的情况下才使用 `!important`。
- **命名约定**：遵循 BEM (Block Element Modifier) 命名规则，以提高代码可读性和可维护性。

### 数据管理规范

所有数据均维护在 `meta/mock.json`中，从主文件的 props 传入到子组件中进行消费。所有组件均**不可以**自行传入模拟数据。

#### Mock 数据

- **存放位置**：所有模拟数据应当存放在 `meta/mock.json` 文件中，mock.json 的数据会在编译时通过主文件入口的 props 传入，不可以代码中硬编码传入的模拟数据。
- **数据结构定义**：依据组件 Props 的类型定义来构建相应的数据结构。
- **一致性要求**：确保 mock 数据与实际接口返回的数据结构保持一致。

#### Schema 定义

- **Schema 规范**: 从 mcp server 获取配置规范
- **文件路径**：Schema 文件位于 `meta/schema.json`。
- **同步更新**：保证 Schema 文件与 mock 数据之间的一致性，任何一方发生变化时都应及时调整另一方。

#### Snippets 定义

- **Snippets 规范**: 从 mcp server 获取配置规范
- **文件路径**：Snippets 文件位于 `meta/snippets.json`。
- **同步更新**：保证 snippets 文件与 schema 之间的一致性，任何一方发生变化时都应及时调整另一方。

### 文档规范

#### 代码注释

- **JSDoc 标准**：每个组件或函数都需要附带 JSDoc 格式的注释。
- **复杂逻辑说明**：复杂的业务逻辑部分，需要添加详细的解释性注释。
- **类型定义注释**：对自定义类型或接口等进行必要的描述性注释。

#### README 维护

- **及时更新**：随着项目的进展，定期检查并更新 README.md 文件内容。
- **包含信息**：
  - 安装指南
  - 使用方法介绍
  - 代理以及调试方法（可选）
  - 记录每次重要的版本变更日志

## 代码编写规范

1. **代码参考模板**：`src`目录下的所有代码仅作为模版用于参考，无需保留其中的内容，严禁在 index.tsx 中导出多个函数组件。
2. **确保无错误**：在进行下一步之前，必须保证没有 TypeScript（ts）编译错误。
3. **原子组件和模块**：始终创建尽可能小且功能独立的组件和模块。
4. **模块化设计**：将功能分解为逻辑清晰、可复用的部分。如果某个文件超过 250 行，请立即对其进行重构。
5. **组件存放位置**：所有模块化的组件应存放在`src/component`文件夹下。
6. **使用指定组件库**：开发过程中只能使用`@ali/adc`组件库，禁止使用 Ant Design (antd) 或 Fusion 组件库。`@ali/adc`组件库的使用方式可以通过查看`node_modules/@ali/adc/`文件夹下所有内容来获取。若`@ali/adc`中没有合适的组件，则直接使用原生 HTML 元素。
7. **解决 TS 报错**：遇到 TypeScript 报错时，优先检查`@ali/adc`组件库是否有相似或可替代的组件。可以通过查看`adc`文档或`node_modules/@ali/adc/`文件夹来获取组件的具体使用方法。
8. **慎用全局样式覆盖**：除非绝对必要，否则不要使用`!important`来全局覆盖样式。
9. **样式隔离**：为了实现更好的样式隔离，首选使用`index.module.css`而非`index.less`来控制组件内的样式。
10. **主入口文件**：`index.tsx`是应用的主入口点，无需按照传统 React 应用的方式调用组件。
11. **更新 mock 数据**：根据 props 的 TypeScript 声明生成键值对数据，并将其更新到项目目录下的`meta/mock.json`文件中。
12. **更新 schema 数据**：

- 获取 schema.json 规范
- 按照规范更新 mock.json 对应的 schema.json

13. **更新 snippets 数据**：

- 获取 snippets.json 规范
- 按照规范更新对应的 snippets.json

## 需求开发规范

1. **安装依赖**：如果代码仓库中尚未安装依赖项，请先执行相应的命令安装所有必要的依赖包。
2. **预览验证**：在确认代码没有任何错误之后，务必通过运行如`tnpm run dev`或`tnpm run start`等命令启动开发服务器，在浏览器中预览并验证效果。
3. **新增需求**：新增组件时确保需求详情和代码完全匹配，确保组件内容完整、无多余内容。
