# 使用图标

## 图标使用完整流程

1. 从 jsx-structure.md 或 dsl.json 中确定需要的图标名称
2. 在 icon-list.csv 中搜索图标，确认 iconType
3. 根据 iconType 选择对应的 npm 包
   - global: 通用类图标, 包括箭头、社交活动、电商、用户、编辑、通知消息等 `@ali/adc-icons`
   - biz: 与业务相关的图标，如产品详情、文档、解决方案等 `@ali/adc-biz-icons`
   - product:与产品相关的图标, 如ECS、RDS、OSS等具体云产品图标 `@ali/adc-product-icons`
4. 正确导入和使用图标组件
5. 根据 dsl.json 中的尺寸要求设置样式

## 图标尺寸设置示例

```jsx
<ArrowRightOutlined style={{ fontSize: '16px' }} />
```

# 注意事项

- 不能直接使用 `@ali/adc-icons` 导出的 `Icon` 组件，它仅在需要自定义传入 svg 图标时使用
