---
description: 文档编写规范
globs: README.md,CHANGELOG.md
alwaysApply: false
---
# 文档规范

- 基于项目源码，主要是 src/、meta/ 目录里的代码，结合用户git 提交历史记录，来编写文档。
- 文档主要分为两个： [README.md](mdc:README.md) 项目文档， [CHANGELOG.md](mdc:CHANGELOG.md) 版本变更文档

## 编写要求

- 避免内容重复和冗余，突出重点
- 保持 Markdown 语法规范，层级清晰
- 涉及代码，需要用代码块展示
- 相关资源建议用超链接
 

## Readme.md 编写规范

在每次完成编码之后，判断是否有必要更新 Readme.md 文档，并遵循以下结构和内容要点：

### 结构要求

1. 物料简介：简明描述用途和适用场景
2. 功能特性：仅提炼需求相关的功能点，避免编码实现细节
3. 快速开始：包含依赖安装、调试/构建/检查等常用命令
4. 目录结构：两层目录结构，重点说明 src、meta 下主要文件
5. 配置说明：schema.json 中 props 的第一层配置字段，以markdown 表格形式给出(包括字段名、类型、注释)
9. 相关链接：Aone 需求、设计稿等平台地址、Git仓库地址、O2 应用地址等所有提及到的项目相关的访问链接

### 内容规范

- 功能特性只写需求点，不写编码实现
- 目录结构和文件说明只描述功能归属，不涉及代码细节
- FAQ、依赖、props 类型声明、样式引入、组件用法等内容可省略
- 相关平台链接需在文档结尾单独列出
- 不要直接复制需求全文，只需简明提炼
- 如有特殊接入/二开说明，可单独补充


## 示例结构

```
# 物料
功能简介

## 功能特性
- 功能点1
- 功能点2

## 快速开始

## 目录结构

## 相关链接
- Aone 需求地址
- 设计稿地址
- O2 地址
- git 仓库访问地址

```


## CHANGELOG.md 编写规范

### 编写要求

- 按照版本号倒序编写，最新的版本号在前面
- 更新 Changelog.md，并按照以下格式进行更新:

编写示例：

```
## v1.0.0
- FEAT: 初始化应用 (@aone_id)
- FIX: 修复 Bug ([64428077](mdc:https:/project.aone.alibaba-inc.com/v2/project/1103280/bug/64428077))
```

