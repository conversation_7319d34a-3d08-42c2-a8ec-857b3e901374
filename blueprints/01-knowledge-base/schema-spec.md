# schema 协议

本 schema 用于描述物料组件的入参配置，遵循 JSON Schema 标准。下方详细说明每个字段的含义和用法，并配有示例。

## 1. schema 示例

```json
{
  "type": "biu",
  "title": "示例组件",
  "screenshot": "",
  "props": {
    "name": {
      "type": "string",
      "title": "名字"
    }
  }
}
```

- type：必须为 'biu'，表示鸿蒙物料类型
- title：组件名称
- screenshot：组件截图的 CDN 地址
- props：组件属性配置，需与组件导出的 props 一致

---

## 2. props 支持的类型及配置

### 2.1 短文本

```json
{
  "shortText": {
    "type": "string",
    "title": "短文本测试",
    "maxLength": 20,
    "minLength": 10,
    "default": "123",
    "description": "请输入最多20个字",
    "x-const": false
  }
}
```

- type: 'string'
- maxLength/minLength: 字符长度限制
- default: 默认值
- description: 占位符/描述
- x-const: 是否常量

---

### 2.2 长文本

```json
{
  "longText": {
    "type": "string",
    "x-format": "textarea",
    "title": "长文本测试",
    "default": "123",
    "description": "请输入最多20个字",
    "x-const": false
  }
}
```

- x-format: 'textarea' 指定为多行文本

---

### 2.3 富文本

```json
{
  "richText": {
    "type": "string",
    "title": "规则",
    "x-format": "richtext",
    "x-format-options": {
      "control": [
        ["header", [2, 3, 4]],
        ["bold"],
        ["indent", ["+1", "-1"]],
        ["align", ["", "center", "right"]],
        ["list", ["ordered", "bullet"]],
        ["highlight"],
        ["link"],
        ["image"]
      ]
    }
  }
}
```

- x-format: 'richtext' 指定为富文本
- x-format-options.control: 控件列表，**请按需传入**，不要全部使用，避免样式维护问题

控件支持表：

| 控件      | 名称 | 可选值                | 输出结构示例                                            |
| --------- | ---- | --------------------- | ------------------------------------------------------- |
| header    | 标题 | 1,2,3,4,5,6           | <h2>标题2</h2>                                          |
| bold      | 加粗 | /                     | <strong>加粗</strong>                                   |
| highlight | 高亮 | /                     | <em>高亮</em>                                           |
| link      | 链接 | /                     | <a href="...">链接</a>                                  |
| indent    | 缩进 | '+1','-1'             | <p class="ql-indent-1">缩进1</p>                        |
| align     | 对齐 | '', 'center', 'right' | <p class="ql-align-center">居中</p>                     |
| list      | 列表 | 'ordered','bullet'    | <ol><li>有序</li></ol>                                  |
| image     | 图片 | /                     | <div class="ql-image-container"><img src="xxx" /></div> |

---

### 2.4 单选枚举

```json
{
  "select": {
    "type": "string",
    "title": "枚举设置器测试",
    "enum": ["0", "1"],
    "x-enum-name": ["横向", "倾斜"],
    "default": "0",
    "description": "请选择",
    "x-const": false
  }
}
```

- enum: 可选值
- x-enum-name: 显示名称

---

### 2.5 多选枚举

```json
{
  "multipleArray": {
    "type": "array",
    "title": "多选",
    "items": {
      "type": "string",
      "enum": [0, 1, 2],
      "x-enum-name": ["默认阴影", "大促阴影", "活动阴影"]
    },
    "default": [0, 1],
    "description": "请选择",
    "x-const": false
  }
}
```

- type: 'array'，items 配置同单选

---

### 2.6 布尔类型

```json
{
  "bool": {
    "type": "boolean",
    "title": "boolean设置器测试",
    "default": false,
    "x-const": false
  }
}
```

---

### 2.7 数字类型

```json
{
  "number": {
    "type": "number",
    "title": "number设置器测试",
    "min": 10,
    "max": 200,
    "description": "请输入",
    "default": 123,
    "x-const": false,
    "x-step": 1
  }
}
```

- min/max: 数值范围
- x-step: 步长

---

### 2.8 数组类型

```json
{
  "array": {
    "type": "array",
    "title": "array设置器测试",
    "items": {
      "type": "object",
      "title": "每项描述文案",
      "properties": {
        "title": {
          "type": "string",
          "title": "标题"
        },
        "description": {
          "type": "string",
          "title": "描述"
        },
        "name": {
          "type": "string",
          "title": "名字"
        }
      },
      "required": ["title", "description"],
      "default": {
        "title": "I am title",
        "description": "I am des"
      }
    }
  }
}
```

- items：数组每一项的配置
- required：必填项，数组类型 items 的 required 字段内需要至少传一项（建议最多两项），否则新增数组项可能会报错
- default：每一项的默认值

---

### 2.9 对象类型

```json
{
  "object": {
    "type": "object",
    "title": "一级对象",
    "properties": {
      "maxWidth": {
        "type": "string",
        "title": "组件宽度"
      },
      "bgImg": {
        "type": "string",
        "title": "PC端背景图"
      },
      "sendObj": {
        "type": "object",
        "title": "二级对象",
        "properties": {
          "sendname": {
            "type": "string",
            "title": "名称"
          }
        }
      }
    }
  }
}
```

---

### 2.10 模型设置器

用于进一步指定 schema 的数据来源，通常schema的字段，需要由低代码引擎配置。
通过模型设置器配置，可以指出哪些字段可以有信息中心提供，在配置对应字段时，会弹出信息中心的配置面板，之后自动填充数据。

1. 通过 `x-format` 制定当前对象可以使用信息中心的关联模型
2. 通过 `x-format-options` 制定当前对象的属性和信息中心的关联模型字段的hash值建立对应关系

```json5
{
  productInfo: {
    type: 'object',
    title: '产品信息',
    properties: {
      pipCode: {
        type: 'string',
        title: '产品code',
      },
      freeTrialUrl: {
        type: 'string',
        title: '试用页地址',
      },
      buyUrl: {
        type: 'string',
        title: '购买地址',
      },
      openUrl: {
        type: 'string',
        title: '开通地址',
      },
      pricingUrl: {
        type: 'string',
        title: '定价详情页',
      },
      billingItemList: {
        type: 'array',
        title: '计费方式列表',
        item: {
          billingType: {
            type: 'string',
            title: '计费类型',
          },
          billingDetail: {
            type: 'array',
            title: '计费描述',
            item: {
              name: {
                type: 'string',
                title: '名称',
              },
              description: {
                type: 'string',
                title: '描述',
              },
            },
          },
          billingUrl: {
            type: 'string',
            title: '计费详情页地址',
          },
        },
      },
    },
    'x-format': 'model',
    'x-format-options': {
      id: 'model-d60b227449c17caf5d1c',
      properties: [
        {
          name: 'pipCode',
          mapName: 'uo5x926gbob6tuu76lo9',
        },
        {
          name: 'freeTrialUrl',
          mapName: '7q2vea9l8lik3po0fvn5',
        },
        {
          name: 'buyUrl',
          mapName: '6xpe65qyew5w5lltpayu',
        },
        {
          name: 'openUrl',
          mapName: 'cx59yq7ud4hieqfned8g',
        },
        {
          name: 'pricingUrl',
          mapName: '33lxd303b9jtkt7qdujq',
        },
        {
          name: 'billingItemList[$0].billingType',
          mapName: 'd799id4yo1m66fob0vpo[*].5wr5r8txunq2qymqwt0a ',
        },
        {
          // 当前要匹配的字段是对象数组里的字段
          name: 'billingItemList[$0].billingUrl',
          // 信息中心中的模型是对象数组且要匹配这个数组中指定字段
          mapName: 'd799id4yo1m66fob0vpo[*].g9rp05eiqcfy4v99cs67',
        },
        {
          // 当前要匹配的字段是对象数组里多层数组的场景，需要指定多层，通过 $0, $1 依次指定层次
          name: 'billingItemList[$0].billingDetail[$1].name',
          mapName: 'd799id4yo1m66fob0vpo[*].ofk32h5ylc0mf41nd88g[*].uf4pgpi1wqbai98bfpt4',
        },
        {
          name: 'billingItemList[$0].billingDetail[$1].description',
          mapName: 'd799id4yo1m66fob0vpo[*].ofk32h5ylc0mf41nd88g[*].ca1uy3ny8yh4tkkbd6wm',
        },
      ],
    },
  },
}
```

- type/object/title/properties：基础设置器方法正常编写数据的结构
- x-format：关联信息结构化平台模型数据
- x-format-options.id：模型 id
- x-format-options.properties.name：上面定义的数据字段
- x-format-options.properties.mapName：信息模型中对应字段的 key 值
