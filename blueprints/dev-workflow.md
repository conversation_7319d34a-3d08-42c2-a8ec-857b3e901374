# 物料开发指引

严格按照如下流程，逐步完成当前物料模块的开发。每个步骤都有明确的**输入**、**执行**和**验证**要求。

## 执行原则

1. **按顺序执行**：严格按照步骤1-8的顺序执行，不可跳跃
2. **完整验证**：每个步骤完成后必须进行验证检查
3. **文件完整性**：确保生成的每个文件都符合规范要求
4. **代码质量**：所有代码必须通过lint检查和格式化

---

## 1. 项目初始化

### 执行步骤：

1. **读取项目元信息**：

   - 阅读 `02-project-setup/metadata.json` 获取项目基本信息
   - 理解项目名称、npm包名、作者、版本等信息

2. **执行初始化**：

   - 根据 `02-project-setup/initialization-guide.md` 在当前项目下执行初始化
   - 解压 `02-project-setup/template.zip` 到项目根目录
   - 安装依赖：执行命令 `tnpm install`

3. **理解项目结构**：
   - 学习 `02-project-setup/architecture-spec.md` 了解目录结构规范

### 验证检查：

- [ ] template.zip 已解压完成
- [ ] package.json 文件存在且包含正确的包名
- [ ] node_modules 目录存在
- [ ] 项目目录结构符合 architecture-spec.md 规范

---

## 2. 需求理解

### 执行步骤：

1. **阅读原始需求**：

   - 仔细阅读 `03-requirements/requirements-spec.md`
   - 理解功能描述、数据结构、UI交互细节

2. **理解项目元信息**：

   - 再次确认 `02-project-setup/metadata.json` 中的项目信息

3. **更新项目描述**：
   - 根据需求更新 `package.json` 中的 `description` 字段
   - 更新 `README.md` 中的项目名称和描述部分

### 验证检查：

- [ ] 已完全理解需求的功能点
- [ ] package.json 的 description 已更新
- [ ] README.md 的项目描述已更新
- [ ] 明确了数据结构和UI要求

---

## 3. 生成低代码 schema.json 数据

### 执行步骤：

1. **学习规范**：

   - 阅读 `01-knowledge-base/schema-spec.md` 了解schema编写规范

2. **理解数据模型**：
   - 参考需求中提供的如下信息中心关于常见问题模型结构描述，理解字段含义

**常见问题的信息中心模型结构**

```json
{
  "data": {
    "required": [],
    "modelId": "model-b1ec7f4191a02531ce1e",
    "version": "",
    "primaryKey": "y3nnz98yp4ogg473q94g",
    "type": "object",
    "properties": {
      "y3nnz98yp4ogg473q94g": {
        "type": "string",
        "title": "提问",
        "description": ""
      },
      "8kwg6lm9hulr4ja5ao8i": {
        "type": "string",
        "title": "回答",
        "description": ""
      },
      "ac9fnxmq3vscu515i1qu": {
        "type": "string",
        "title": "详情页地址",
        "description": ""
      },
      "tc6z6bsif0r1ai8lmgba": {
        "type": "string",
        "title": "所属分类",
        "description": ""
      },
      "fuzz12k8bj1tz0y7scn0": {
        "type": "string",
        "title": "产品code",
        "description": ""
      }
    }
  }
}
```

**常见问题的信息中心数据实例，展示2条常见问题及其答案和跳转链接**

```json
[
  {
    "y3nnz98yp4ogg473q94g": "阿里巴巴有计划把Dragonwell的独有patch贡献到OpenJDK上游吗？",
    "8kwg6lm9hulr4ja5ao8i": "是的，阿里巴巴JVM团队一直在尝试向OpenJDK社区贡献patch，包括大的特性和较小的Bug修复。",
    "ac9fnxmq3vscu515i1qu": "https://github.com/dragonwell-project/dragonwell11/wiki/%E9%98%BF%E9%87%8C%E5%B7%B4%E5%B7%B4Dragonwell11%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98",
    "fuzz12k8bj1tz0y7scn0": "dragonwell"
  },
  {
    "y3nnz98yp4ogg473q94g": "云数据库 Tair（兼容 Redis）支持操作日志审计吗？",
    "8kwg6lm9hulr4ja5ao8i": "云数据库Tair（兼容 Redis）实例基于阿里云日志服务SLS（Log Service），推出审计日志功能，为您提供日志的查询、在线分析、导出等功能，既可以帮助安全审计人员及时发现数据异常操作的行为，快速定位修改数据的用户身份与时间点，又可以助力业务系统满足安全性与合规性的要求，还能帮助开发运维人员定位性能问题。",
    "ac9fnxmq3vscu515i1qu": "https://help.aliyun.com/zh/redis/user-guide/enable-the-new-audit-log-feature/",
    "tc6z6bsif0r1ai8lmgba": "安全合规",
    "fuzz12k8bj1tz0y7scn0": "kvstore"
  },
  {
    "y3nnz98yp4ogg473q94g": "日志服务 SLS有几种计费方式",
    "8kwg6lm9hulr4ja5ao8i": "日志服务 SLS按量计费方式区分按写入数据量计费和按使用功能计费两种模式，新版资源包-预付计划2.0同时支持按使用功能计费模式和按写入数据量计费模式。支持按量付费和资源包两种计费方式",
    "ac9fnxmq3vscu515i1qu": "https://help.aliyun.com/zh/sls/product-overview/billing-overview",
    "fuzz12k8bj1tz0y7scn0": "sls"
  },
  {
    "y3nnz98yp4ogg473q94g": "是否支持集群的扩缩容？",
    "8kwg6lm9hulr4ja5ao8i": "支持，但需注意以下信息：  扩容：仅支持扩展Core节点和Task节点，且新扩展节点的配置默认与已有节点一致。您在扩容时，需确认对应订单已经全部支付，如果存在待支付订单，将造成扩容失败。扩容具体操作，请参见扩容集群。  缩容：  对按量付费类型的Task节点组：缩容具体操作，请参见缩容集群。  对于包年包月类型的Task节点组或Core节点组：由于包年包月ECS实例需手工退订以及Core节点组会存储HDFS数据，需手工执行decommission操作。缩容具体操作，请参见手工缩容节点组。",
    "ac9fnxmq3vscu515i1qu": "https://help.aliyun.com/zh/emr/emr-on-ecs/user-guide/faq-about-cluster-management#section-1b3-l22-888",
    "fuzz12k8bj1tz0y7scn0": "emapreduce"
  },
  {
    "y3nnz98yp4ogg473q94g": "阿里云服务器s6/c6/g6/r6/u1/c7/g7/r7实例规格",
    "8kwg6lm9hulr4ja5ao8i": "不同规格的实例适用场景有差异，例如S6适用于计算密集型场景、C6适用于需要高性能计算和多线程计算的场景等，下文供大家参考来选择实例。",
    "ac9fnxmq3vscu515i1qu": "https://developer.aliyun.com/article/1153484",
    "tc6z6bsif0r1ai8lmgba": "选型2",
    "fuzz12k8bj1tz0y7scn0": "ecs"
  }
]
```

### **编写schema.json**：

- 在 `meta/` 目录下创建 `schema.json`
- 根据需求配置数据结构，包含：
  - 常见问题分类列表（支持3-5个分类）
  - 每个分类下的问答对列表
  - 跳转卡片的标题和链接配置
- 正确配置 `x-format` 和 `x-format-options`

### **模型设置器配置规范**：

**关键规则**：

1. **层级位置**：`x-format` 和 `x-format-options` 必须与 `type: "array"` 在同一层级
2. **字段映射**：建立schema字段与信息中心字段的正确映射关系
3. **数据约束**：为所有字段添加合理的长度限制和默认值

**正确配置示例**：

```json
{
  "questions": {
    "type": "array",                    // array类型声明
    "x-format": "model",                // 模型设置器标识
    "x-format-options": {               // 模型配置选项
      "id": "model-b1ec7f4191a02531ce1e",
      "properties": [
        {
          "name": "question",           // schema字段名
          "mapName": "y3nnz98yp4ogg473q94g"  // 信息中心字段hash
        }
      ]
    },
    "items": {                          // 数组项定义
      "type": "object",
      "properties": { ... }
    }
  }
}
```

**常见错误**：

- ❌ 将模型设置器配置放在 `items` 对象内部
- ❌ 字段映射关系不正确
- ❌ 缺少必要的数据约束

### 验证检查：

- [ ] schema.json 文件已创建
- [ ] 数据结构符合需求描述
- [ ] 包含常见问题分类和问答对配置
- [ ] 包含跳转卡片配置
- [ ] x-format 和 x-format-options 配置符合 schema 规范
- [ ] **模型设置器配置层级正确**：与 `type: "array"` 同级
- [ ] **字段映射完整**：所有必要字段都有对应的 mapName

## 4. 生成 mock.json

### 执行步骤：

1. **基于schema生成数据**：

   - 根据上一步生成的 `schema.json` 数据格式,参考需求中提供的信息中心数据示例, 在 `meta/` 目录下创建 `mock.json`, 生成 mock 数据
   - 如果信息中心的数据没有覆盖所有字段，则从 metadata.json 中的预览图里，识别相关文本，尝试对应未覆盖的字段

2. **数据内容要求**：

   - 至少包含3个问题分类
   - 每个分类至少包含3个问答对
   - 跳转卡片包含标题和链接

3. **数据一致性保证**：
   - 确保 mock.json 中的所有字段与schema.json中定义的字段完全匹配
   - 验证数据类型和格式符合schema约束

### 验证检查：

- [ ] mock.json 文件已创建
- [ ] 数据格式与 schema.json 一致
- [ ] 包含真实的问答内容
- [ ] **字段完整性**：包含schema中定义的所有字段
- [ ] **数据类型正确**：所有字段类型符合schema定义

---

## 5. 生成 snippets.json

### 执行步骤：

1. **学习规范**：

   - 阅读 `01-knowledge-base/snippets-spec.md` 了解snippets编写规范

2. **生成snippets**：

   - 在 `meta/` 目录下创建 `snippets.json`
   - 基于 schema.json 和 mock.json 生成默认配置
   - 更新 screenshots 图片地址为元信息中的预览图地址

3. **数据一致性校验**：
   - 确保 snippets.json 的数据结构与 schema.json 完全一致
   - 验证默认数据的有效性
   - 确保snippets中的数据格式与mock.json保持一致

### 验证检查：

- [ ] snippets.json 文件已创建
- [ ] 数据结构与 schema.json 一致
- [ ] screenshots 地址正确
- [ ] 默认数据有效
- [ ] **字段完整性**：所有示例数据包含schema中定义的字段

---

## 6. 生成 src 的代码

**目标**：结合需求内容和 schema.json 定制的数据结构，完成src部分的组件源码开发。

### 6.1 学习基础知识

**执行步骤**：

1. 学习 `01-knowledge-base/foundation-guide.md` - 项目基础
2. 学习 `01-knowledge-base/best-practices.md` - 最佳实践
3. 学习 `01-knowledge-base/com-design-guide.md` - 基础组件使用指导

**验证检查**：

- [ ] 理解COM Design组件库的使用方法
- [ ] 掌握响应式布局原则
- [ ] 了解组件开发规范

### 6.2 完成 UI 代码

**执行步骤**：

1. **理解需求**：

   - 结合原始需求、metadata.json、schema.json 理解数据结构

2. **实现基础UI**：

   - 按照 `04-design/jsx-structure.md` 结构，参考 `04-design/api-reference/index.md` 的组件API指引， 在 `src/index.tsx` 中按照 JSX 结构还原设计稿
   - 注意除了布局类组件可以微调外，JSX 结构中的元素不要修改
   - 注意：当JSX结构和需求有冲突时，JSX 结构的置信度更高

3. **处理图标**：

   - **设计稿对比验证**：严格对照 `04-design/jsx-structure.md` 中的图标使用，确保所有图标都在实现中正确对应
     - **图标类型识别**：使用 `01-knowledge-base/icon-list.csv` 确定图标的 `iconType` 字段，选择正确的npm包：
     - `global`: 使用 `@ali/adc-icons`
     - `biz`: 使用 `@ali/adc-biz-icons`
     - `product`: 使用 `@ali/adc-product-icons`
   - **图标规范使用**：参考 `01-knowledge-base/icon-guide.md` 调整图标代码
   - **尺寸样式优化**：基于 `dsl.json` 中的尺寸描述，为图标添加 `font-size` 样式
   - **导入声明检查**：确保所有使用的图标都已正确导入对应的npm包

4. **细节优化**：

   - 下载项目元数据中的预览图片作为参考
   - 对比预览图调整UI细节（文本颜色、文本尺寸、间距等）
   - 保持整体布局不变，仅做细节微调，检查如下内容：
     - 容器的垂直对齐方向是否和预览图一致
     - 容器的水平对齐方向是否和预览图一致

5. **组件拆分**：
   - 如果 `src/index.tsx` 超过200行，按单一职责原则拆分
   - 在 `/src/components/` 创建子组件文件夹
   - 将相关逻辑拆分到子组件中

**验证检查**：

- [ ] src/index.tsx 文件已创建
- [ ] `04-design/jsx-structure.md` 中的 jsx 组件结构和 src/index.tsx 存在对应关系，没有丢失元素
- [ ] **图标设计稿一致性**：`04-design/jsx-structure.md` 中的所有图标都在UI中正确实现
- [ ] **图标npm包正确性**：所有图标都从正确的npm包导入（global/biz/product对应不同包）
- [ ] **图标尺寸样式**：图标添加了与 `dsl.json` 中描述尺寸一致的css样式
- [ ] **图标导入完整性**：所有使用的图标都已在文件顶部正确导入
- [ ] 代码结构清晰（如需要已拆分子组件）

### 6.3 数据逻辑渲染

**执行步骤**：

1. **类型声明**：

   - 在 `/src/types/index.ts` 定义组件Props类型
   - 确保类型与 schema.json 数据结构完全一致
   - 如果类型较多，可拆分成多个类型文件

2. **数据填充**：

   - 将UI代码中的静态内容替换为props数据
   - 确保所有数据都来自props，不直接引入mock数据
   - 如果是数组类数据，每次遍历时注意考虑其可能为 undefined 或者为空数组的情况，避免 js 运行报错

   示例

   ```jsx
   <div>{list?.length > 0 ? list.map() : null}</div>
   ```

3. **完善交互逻辑**
   - 根据需求内容，完善相关元素的点击、hover等交互效果

**验证检查**：

- [ ] 类型声明文件已创建
- [ ] Props类型与schema一致
- [ ] 所有的props的数组类型，都做好了其为可能为 undefined 的判断
- [ ] UI使用props数据而非静态内容
- [ ] 没有直接引入mock数据

### 6.4 代码审查

**执行步骤**：

1. 执行 prettier 格式化
2. 执行 `npm run format` 进行代码格式化
3. 执行 `npm run lint` 进行代码检查
4. 检查并移除未使用的变量和导入
5. 执行 `npm run dev` 确认调试页面正常运行(启动端口通常在8000~8005)

**验证检查**：

- [ ] 代码格式化完成
- [ ] 无lint错误
- [ ] 无未使用变量
- [ ] 开发服务器可正常启动
- [ ] 页面渲染正常

---

## 7. 完善文档

按照 `01-knowledge-base/doc-standards.md` 完善文档

### 7.1 完善README文档

**执行步骤**：

1. **标题和描述**：

   - 一级标题使用npm包名
   - 基于需求简要描述物料功能

2. **预览图**：

   - 添加元信息中的预览图，宽度固定为600px

3. **配置数据说明**：

   - 创建markdown表格展示schema.json字段
   - 包含：字段名、中文描述、类型、默认值、数据来源
   - 多层级用多个表格展示

4. **开发信息**：

   - 添加项目安装和调试命令

5. **开发相关信息**
   - 列出上下文中提到的关于完成此次需求的相关链接和平台，包括但不限于：设计稿、需求稿地址、git仓库地址等

**验证检查**：

- [ ] README.md 内容完整
- [ ] 预览图显示正常
- [ ] 配置说明清晰
- [ ] 开发命令编写正确

### 7.2 更新CHANGELOG

**执行步骤**：

- 在 `CHANGELOG.md` 中添加当前版本的功能点
- 基于需求描述编写变更日志

**验证检查**：

- [ ] CHANGELOG.md 已更新
- [ ] 功能点描述准确

---

## 8. 全局反思

### 8.1 项目结构检查

**执行步骤**：

1. 检查项目目录结构是否符合 `02-project-setup/architecture-spec.md` 规范
2. 调整不符合规范的文件或目录

**验证检查**：

- [ ] 目录结构符合规范
- [ ] 文件命名正确
- [ ] 组件结构合理

### 8.2 代码质量检查

**执行步骤**：

1. **Linter检查**：修正所有类型和属性用法错误
2. **样式引入**：确保 `src/index.tsx` 中引入 `@ali/adc/es/index.css`
3. **Props一致性**：确保 `src/index.tsx` 的props类型与 mock/schema/snippets 完全一致
4. **引入检查**：检查所有 `*.tsx` 文件的引入是否正确
5. **扩展名检查**: 检查所有的 `*.tsx`，如果文件里未使用jsx代码，应该统一成 `*.ts` 和 `*.js`
6. **图标规范检查**：
   - 验证图标与设计稿的一致性
   - 确保图标npm包选择正确（global/biz/product对应不同包）
   - 检查图标导入声明的完整性
   - 验证图标尺寸样式符合dsl.json要求
7. **数据结构一致性**：验证所有meta文件（schema/mock/snippets）的数据结构完全一致
8. **JSON格式验证**：使用 `JSON.parse()` 验证所有JSON文件格式正确

**验证检查**：

- [ ] 无linter错误
- [ ] 样式文件正确引入
- [ ] Props类型一致
- [ ] 组件引入正确
- [ ] **数据结构一致性**：schema/mock/snippets 三个文件数据结构完全匹配
- [ ] **构建成功**：npm run build:npm 无错误

### 8.3 文档完整性检查

**执行步骤**：

1. 检查 README.md 和 CHANGELOG.md 是否完整
2. 确保所有必要信息都已包含

**验证检查**：

- [ ] 文档内容完整
- [ ] 信息准确无误

---

## 常见问题处理

### 问题1：依赖安装失败

**解决方案**：

- 检查网络连接
- 尝试使用 `npm install` 代替 `tnpm install`
- 清除缓存：`npm cache clean --force`

### 问题2：类型错误

**解决方案**：

- 确保 TypeScript 版本兼容
- 检查 @ali/adc 组件的类型定义
- 重新安装类型包：`npm install @types/react @types/react-dom`

### 问题3：样式不生效

**解决方案**：

- 确保引入了 `@ali/adc/es/index.css`
- 检查CSS Modules配置
- 验证样式文件路径

### 问题4：响应式布局异常

**解决方案**：

- 检查Container组件是否正确使用
- 验证sm/lg属性配置
- 测试不同屏幕尺寸下的表现

### 问题5：模型设置器配置错误

**解决方案**：

- 确保 `x-format` 和 `x-format-options` 与 `type: "array"` 在同一层级
- 检查字段映射关系是否正确：schema字段名对应信息中心字段hash
- 验证模型ID是否与需求中提供的一致
- 使用JSON验证命令检查格式：`node -e "JSON.parse(require('fs').readFileSync('meta/schema.json'))"`

### 问题6：数据结构不一致

**解决方案**：

- 确保schema.json、mock.json、snippets.json三个文件的数据结构完全匹配
- 新增字段时，同步更新所有相关文件
- 验证TypeScript接口定义与schema保持一致
- 使用数据一致性检查脚本验证

### 问题7：图标使用错误

**解决方案**：

- **设计稿对比**：严格对照 `04-design/jsx-structure.md` 确认所有图标名称
- **图标类型查询**：在 `01-knowledge-base/icon-list.csv` 中搜索图标，确认 `iconType` 字段
- **npm包选择**：根据图标类型选择正确的包：
  - `global` → `@ali/adc-icons`
  - `biz` → `@ali/adc-biz-icons`
  - `product` → `@ali/adc-product-icons`
- **导入检查**：确保所有使用的图标都已正确导入
- **验证命令**：
  ```bash
  # 检查图标导入
  grep -r "import.*icons" src/
  # 检查图标使用
  grep -r "<[A-Z].*Outlined\|<[A-Z].*Filled" src/
  ```

## 执行检查清单

完成开发后，请确认以下所有项目都已完成：

### 文件生成检查

- [ ] meta/schema.json
- [ ] meta/mock.json
- [ ] meta/snippets.json
- [ ] src/index.tsx
- [ ] src/types/index.ts
- [ ] README.md (已更新)
- [ ] CHANGELOG.md (已更新)

### 代码质量检查

- [ ] 所有文件通过lint检查
- [ ] 代码已格式化
- [ ] 无未使用变量
- [ ] 类型声明完整
- [ ] **图标使用正确**：与设计稿一致，npm包选择正确，导入完整
- [ ] **JSON格式验证通过**：所有meta文件格式正确
- [ ] **数据结构一致**：schema/mock/snippets三文件结构匹配
- [ ] **模型设置器配置正确**：x-format层级位置符合规范

### 功能验证检查

- [ ] npm run dev 正常启动
- [ ] 页面渲染正确
- [ ] 响应式布局正常
- [ ] 数据展示正确

### 文档完整性检查

- [ ] README.md 内容完整
- [ ] 配置说明清晰
- [ ] 预览图正常显示
- [ ] CHANGELOG.md 已更新

**只有当所有检查项都完成时，才能认为开发任务完成。**
