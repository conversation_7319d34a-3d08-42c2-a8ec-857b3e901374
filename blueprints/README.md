# 项目开发引导

## Blueprints 目录结构说明

本目录包含AI生成项目所需的相关知识和需求文档，按功能模块进行组织。

## 目录结构

```
blueprints/
├── 01-knowledge-base/         # 知识库
│   ├── foundation-guide.md   # 项目基础指南
│   ├── best-practices.md     # 开发最佳实践
│   ├── doc-standards.md      # 文档编写标准
│   ├── com-design-guide.md   # 组件库使用指导
│   ├── icon-guide.md         # 图标实现指南
│   ├── icon-list.csv         # 图标列表数据
│   ├── schema-spec.md        # 低代码Schema规范
│   └── snippets-spec.md      # 代码片段规范
├── 02-project-setup/          # 项目初始化设置
│   ├── metadata.json          # 项目基础配置信息
│   ├── initialization-guide.md        # 项目初始化指南
│   └── architecture-spec.md  # 项目架构规范
├── 03-requirements/           # 需求文档
│   └── requirements-spec.md  # 业务需求规范文档
├── 04-design/                 # 设计相关文档
│   ├── dsl.json              # 设计系统语言
│   ├── jsx-structure.md      # 组件结构规范
│   └── api-reference/        # UI组件API参考文档
├── dev-workflow.md           # 物料开发流程指引
└── README.md                 # 本说明文件
```

## 文档说明

- **01-knowledge-base**: 项目开发的完整知识库，包含：
  - 基础知识和最佳实践标准
  - 组件库使用指导和图标实现指南
  - 低代码引擎相关的Schema和代码片段规范
- **02-project-setup**: 包含项目初始化所需的配置文件和指南
- **03-requirements**: 存放业务需求规范和需求分析文档
- **04-design**: 设计系统相关的数据和API参考文档
- **dev-workflow.md**: 完整的物料开发流程指引，是整个开发过程的主要参考文档

## 使用说明

1. 开始项目开发前，请先阅读 `dev-workflow.md`
2. 按照流程指引，依次参考各目录下的相关文档，严格按照步骤完成开发
