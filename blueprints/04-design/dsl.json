{"dsl": {"styles": {"effect_158:5933": {"value": []}, "effect_70:5269": {"value": []}, "paint_56:00782": {"value": ["#E9E9E9"]}, "effect_56:04530": {"value": []}, "paint_2:416": {"value": ["#1366EC"], "token": "text/primary"}, "font_2:242": {"value": {"family": "PingFangSC-Semibold", "size": 14, "decoration": "none", "case": "none", "lineHeight": "24", "letterSpacing": "%"}, "token": "中文-2.正文 Body/强调正文-加粗, 14px"}, "paint_2:378": {"value": ["#181818"], "token": "text/default"}, "font_2:347": {"value": {"family": "PingFangSC-Regular", "size": 14, "decoration": "none", "case": "none", "lineHeight": "24", "letterSpacing": "%"}, "token": "中文-2.正文 Body/常规正文-常规, 14px"}, "paint_9:643": {"value": ["#181818"]}, "font_9:0151": {"value": {"family": "PingFangSC-Semibold", "size": 14, "decoration": "none", "case": "none", "lineHeight": "24", "letterSpacing": "%"}}, "paint_2:356": {"value": ["#717171"], "token": "text/subtlest"}, "paint_56:734": {"value": ["#1366EC"], "token": "link/default"}, "font_56:698": {"value": {"family": "PingFangSC", "size": 14, "style": "Regular", "decoration": "none", "case": "none", "lineHeight": "24", "letterSpacing": "auto"}}, "paint_70:9762": {"value": ["#F4F4F4"]}, "paint_56:00776": {"value": ["#D8D8D8"]}, "font_9:642": {"value": {"family": "PingFangSC-Semibold", "size": 18, "decoration": "none", "case": "none", "lineHeight": "28", "letterSpacing": "%"}, "token": "中文-1.标题 Title/H3 页面标题三-加粗, 18px"}, "effect_158:01628": {"value": []}}, "nodes": [{"type": "INSTANCE", "id": "158:01827", "layoutStyle": {"width": 1413, "height": 388, "relativeX": 0, "relativeY": 0}, "componentInfo": {"componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@0.0.5-beta.1/build/llms/Container.md"], "properties": {"Type": "24"}}, "children": [{"type": "FRAME", "id": "158:01827/158:01797", "name": "slot", "layoutStyle": {"width": 1365, "height": 388, "relativeX": 24}, "flexGrow": 1, "children": [{"type": "INSTANCE", "id": "158:01895", "layoutStyle": {"width": 1365, "height": 388, "relativeX": 0}, "flexGrow": 1, "componentInfo": {"componentSetDescription": "Flex 孩子容器节点推荐使用Box", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Flex.md"], "properties": {"Type": "1", "Columns": "2"}}, "children": [{"type": "FRAME", "id": "158:01895/158:01834", "name": "slot", "layoutStyle": {"width": 976, "height": 388, "relativeX": 0, "relativeY": 0}, "flexGrow": 1, "children": [{"type": "INSTANCE", "id": "158:03240", "layoutStyle": {"width": 976, "height": 388, "relativeX": 0}, "flexGrow": 1, "componentInfo": {"description": "size=\"lg\"", "documentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Tabs.md"], "componentSetDescription": "Tabs[type=\"underline\"]", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Tabs.md"], "properties": {"Size": "L (40)"}}, "children": [{"type": "FRAME", "id": "158:03240/158:5943", "name": "容器 5454", "layoutStyle": {"width": 976, "height": 40}, "flexShrink": 1, "strokeColor": "paint_56:00782", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "0px 0px 1px", "children": [{"type": "INSTANCE", "id": "158:03240/56:15078", "layoutStyle": {"width": 69, "height": 40}, "componentInfo": {"properties": {"Type": "Underline", "Size": "L (40)", "State": "Selected", "Disabled": "False"}}, "children": [{"type": "TEXT", "id": "158:03240/56:15078/56:15078/56:14852", "name": "选项一", "layoutStyle": {"width": 38, "height": 24, "relativeX": 20, "relativeY": 8}, "effect": "effect_56:04530", "text": [{"text": "Tab 1", "font": "font_2:242"}], "textColor": [{"start": 0, "end": 5, "color": "paint_2:416"}], "textAlign": "center", "textMode": "single-line"}], "componentId": "56:15078"}, {"type": "INSTANCE", "id": "158:03240/56:15085", "layoutStyle": {"width": 69, "height": 40, "relativeX": 69}, "componentInfo": {"properties": {"Type": "Underline", "Size": "L (40)", "State": "<PERSON><PERSON><PERSON>", "Disabled": "False"}}, "children": [{"type": "TEXT", "id": "158:03240/56:15085/56:15085/56:14829", "name": "选项一", "layoutStyle": {"width": 40, "height": 24, "relativeX": 20, "relativeY": 8}, "effect": "effect_56:04530", "text": [{"text": "Tab 2", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 5, "color": "paint_2:378"}], "textAlign": "center", "textMode": "single-line"}], "componentId": "56:15085"}, {"type": "INSTANCE", "id": "158:03240/56:15081", "layoutStyle": {"width": 83, "height": 40, "relativeX": 138}, "componentInfo": {"properties": {"Type": "Underline", "Size": "L (40)", "State": "<PERSON><PERSON><PERSON>", "Disabled": "False"}}, "children": [{"type": "TEXT", "id": "158:03240/56:15081/56:15081/56:14829", "name": "选项一", "layoutStyle": {"width": 40, "height": 24, "relativeX": 20, "relativeY": 8}, "effect": "effect_56:04530", "text": [{"text": "Tab 3", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 5, "color": "paint_2:378"}], "textAlign": "center", "textMode": "single-line"}], "componentId": "56:15081"}, {"type": "INSTANCE", "id": "158:03240/158:00941", "layoutStyle": {"width": 98, "height": 40, "relativeX": 221}, "componentInfo": {"properties": {"Type": "Underline", "Size": "L (40)", "State": "<PERSON><PERSON><PERSON>", "Disabled": "False"}}, "children": [{"type": "TEXT", "id": "158:03240/158:00941/158:00941/56:14829", "name": "选项一", "layoutStyle": {"width": 40, "height": 24, "relativeX": 20, "relativeY": 8}, "effect": "effect_56:04530", "text": [{"text": "Tab 4", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 5, "color": "paint_2:378"}], "textAlign": "center", "textMode": "single-line"}], "componentId": "158:00941"}], "effect": "effect_158:5933", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}, {"type": "FRAME", "id": "158:03240/158:5952", "name": "容器 5453", "layoutStyle": {"width": 976, "height": 348, "relativeX": 0, "relativeY": 40}, "flexShrink": 1, "children": [{"type": "FRAME", "id": "158:03261", "name": "容器 5476", "layoutStyle": {"width": 976, "height": 324, "relativeY": 24}, "flexShrink": 1, "children": [{"type": "FRAME", "id": "158:03292", "name": "容器 5473", "layoutStyle": {"width": 976, "height": 116}, "flexShrink": 1, "strokeColor": "paint_56:00782", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "0px 0px 1px", "children": [{"type": "FRAME", "id": "158:03312", "name": "容器 5470", "layoutStyle": {"width": 318, "height": 24, "relativeY": 20}, "children": [{"type": "INSTANCE", "id": "158:03313", "layoutStyle": {"width": 26, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03313/9:0064", "name": "主要按钮", "layoutStyle": {"width": 26, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "Q：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "INSTANCE", "id": "158:03319", "layoutStyle": {"width": 292, "height": 24, "relativeX": 26}, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "default"}}, "children": [{"type": "TEXT", "id": "158:03319/2:375", "name": "主要按钮", "layoutStyle": {"width": 292, "height": 24}, "text": [{"text": "RDS MySQL 与自建 MySQL 数据库对比优势", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 27, "color": "paint_2:378"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "2:374"}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "auto", "crossSizing": "auto"}}, {"type": "FRAME", "id": "158:03293", "name": "容器 5472", "layoutStyle": {"width": 976, "height": 48, "relativeY": 48}, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:03294", "layoutStyle": {"width": 24, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03294/9:0064", "name": "主要按钮", "layoutStyle": {"width": 24, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "A：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "FRAME", "id": "158:03300", "name": "容器 5478", "layoutStyle": {"width": 952, "height": 48, "relativeX": 24}, "flexGrow": 1, "children": [{"type": "INSTANCE", "id": "158:03307", "layoutStyle": {"width": 896, "height": 48}, "flexGrow": 1, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "subtlest"}}, "children": [{"type": "TEXT", "id": "158:03307/2:431", "name": "主要按钮", "layoutStyle": {"width": 896, "height": 48}, "flexShrink": 1, "text": [{"text": "云数据库 RDS MySQL 提供高可用、高可靠、高安全、可扩展的托管数据库服务，同性能的 RDS MySQL 数据库价格相比 ECS 自建 MySQL 数据库和自购服务器搭建 MySQL 数据库更加低廉，能够节约大......", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 114, "color": "paint_2:356"}], "textAlign": "left", "textMode": "auto-height"}], "componentId": "2:430"}, {"type": "INSTANCE", "id": "158:03301", "layoutStyle": {"width": 56, "height": 24, "relativeX": 896, "relativeY": 24}, "componentInfo": {"componentSetDescription": "链接", "properties": {"State": "<PERSON><PERSON><PERSON>", "Size": "M", "Disabled": "False"}}, "children": [{"type": "TEXT", "id": "158:03301/56:11984", "name": " ", "layoutStyle": {"width": 56, "height": 24}, "text": [{"text": "查看详情", "font": "font_56:698"}], "textColor": [{"start": 0, "end": 4, "color": "paint_56:734"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "56:11983"}], "flexContainerInfo": {"flexDirection": "row", "alignItems": "flex-end", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "column", "mainSizing": "auto", "crossSizing": "fixed", "gap": "4px", "padding": "20px 0px"}}, {"type": "FRAME", "id": "158:03324", "name": "容器 5474", "layoutStyle": {"width": 976, "height": 116, "relativeY": 116}, "flexShrink": 1, "strokeColor": "paint_56:00782", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "0px 0px 1px", "children": [{"type": "FRAME", "id": "158:03342", "name": "容器 5470", "layoutStyle": {"width": 328, "height": 24, "relativeY": 20}, "children": [{"type": "INSTANCE", "id": "158:03348", "layoutStyle": {"width": 26, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03348/9:0064", "name": "主要按钮", "layoutStyle": {"width": 26, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "Q：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "INSTANCE", "id": "158:03343", "layoutStyle": {"width": 302, "height": 24, "relativeX": 26}, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "default"}}, "children": [{"type": "TEXT", "id": "158:03343/2:375", "name": "主要按钮", "layoutStyle": {"width": 302, "height": 24}, "text": [{"text": "基础系列、高可用系列、集群系列应该怎么选？", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 21, "color": "paint_2:378"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "2:374"}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "auto", "crossSizing": "auto"}}, {"type": "FRAME", "id": "158:03325", "name": "容器 5472", "layoutStyle": {"width": 976, "height": 48, "relativeY": 48}, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:03326", "layoutStyle": {"width": 24, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03326/9:0064", "name": "主要按钮", "layoutStyle": {"width": 24, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "A：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "FRAME", "id": "158:03332", "name": "容器 5479", "layoutStyle": {"width": 952, "height": 48, "relativeX": 24}, "flexGrow": 1, "children": [{"type": "INSTANCE", "id": "158:03333", "layoutStyle": {"width": 896, "height": 48}, "flexGrow": 1, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "subtlest"}}, "children": [{"type": "TEXT", "id": "158:03333/2:431", "name": "主要按钮", "layoutStyle": {"width": 896, "height": 48}, "flexShrink": 1, "text": [{"text": "基础版为单节点架构，用于微型网站或中小企业开发测试环境。高可用版为一主一备高可用架构，用于大中型企业生产数据库。集群版为一主多备架构，用于有大量流量高峰读请求和数据智能分析需求......", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 94, "color": "paint_2:356"}], "textAlign": "left", "textMode": "auto-height"}], "componentId": "2:430"}, {"type": "TEXT", "id": "158:03338", "name": " ", "layoutStyle": {"width": 56, "height": 24, "relativeX": 896, "relativeY": 24}, "text": [{"text": "查看详情", "font": "font_56:698"}], "textColor": [{"start": 0, "end": 4, "color": "paint_56:734"}], "textAlign": "left", "textMode": "single-line"}], "flexContainerInfo": {"flexDirection": "row", "alignItems": "flex-end", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "column", "mainSizing": "auto", "crossSizing": "fixed", "gap": "4px", "padding": "20px 0px"}}, {"type": "FRAME", "id": "158:03262", "name": "容器 5476", "layoutStyle": {"width": 976, "height": 92, "relativeY": 232}, "flexShrink": 1, "strokeColor": "paint_56:00782", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "0px 0px 1px", "children": [{"type": "FRAME", "id": "158:03280", "name": "容器 5470", "layoutStyle": {"width": 277, "height": 24, "relativeY": 20}, "children": [{"type": "INSTANCE", "id": "158:03281", "layoutStyle": {"width": 26, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03281/9:0064", "name": "主要按钮", "layoutStyle": {"width": 26, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "Q：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "INSTANCE", "id": "158:03287", "layoutStyle": {"width": 251, "height": 24, "relativeX": 26}, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "default"}}, "children": [{"type": "TEXT", "id": "158:03287/2:375", "name": "主要按钮", "layoutStyle": {"width": 251, "height": 24}, "text": [{"text": "RDS 存在什么使用限制和注意事项吗？\r", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 20, "color": "paint_2:378"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "2:374"}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "auto", "crossSizing": "auto"}}, {"type": "FRAME", "id": "158:03263", "name": "容器 5472", "layoutStyle": {"width": 976, "height": 24, "relativeY": 48}, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:03274", "layoutStyle": {"width": 24, "height": 24}, "componentInfo": {"description": "Typography.H6", "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H6"}}, "children": [{"type": "TEXT", "id": "158:03274/9:0064", "name": "主要按钮", "layoutStyle": {"width": 24, "height": 24, "relativeX": 0, "relativeY": 0}, "text": [{"text": "A：", "font": "font_9:0151"}], "textColor": [{"start": 0, "end": 2, "color": "paint_9:643"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "9:0135"}, {"type": "FRAME", "id": "158:03264", "name": "容器 5479", "layoutStyle": {"width": 952, "height": 24, "relativeX": 24}, "flexGrow": 1, "children": [{"type": "INSTANCE", "id": "158:03265", "layoutStyle": {"width": 896, "height": 24}, "flexGrow": 1, "componentInfo": {"componentSetDescription": "正文字体，使用 Typography.Text 子组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "default", "color": "subtlest"}}, "children": [{"type": "TEXT", "id": "158:03265/2:431", "name": "主要按钮", "layoutStyle": {"width": 896, "height": 24}, "flexShrink": 1, "text": [{"text": "在变更配置、版本升级、故障切换时存在闪断，切换内外网时连接会断开，存储空间满实例会被锁定。", "font": "font_2:347"}], "textColor": [{"start": 0, "end": 45, "color": "paint_2:356"}], "textAlign": "left", "textMode": "auto-height"}], "componentId": "2:430"}, {"type": "TEXT", "id": "158:03270", "name": " ", "layoutStyle": {"width": 56, "height": 24, "relativeX": 896}, "text": [{"text": "查看详情", "font": "font_56:698"}], "textColor": [{"start": 0, "end": 4, "color": "paint_56:734"}], "textAlign": "left", "textMode": "single-line"}], "flexContainerInfo": {"flexDirection": "row", "alignItems": "flex-end", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "column", "mainSizing": "auto", "crossSizing": "fixed", "gap": "4px", "padding": "20px 0px"}}], "flexContainerInfo": {"flexDirection": "column", "mainSizing": "auto", "crossSizing": "fixed"}}], "flexContainerInfo": {"flexDirection": "column", "mainSizing": "fixed", "crossSizing": "fixed", "gap": "10px", "padding": "24px 0px 0px"}}], "componentId": "56:15077"}], "effect": "effect_70:5269", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}, {"type": "FRAME", "id": "158:01895/158:01835", "name": "slot", "layoutStyle": {"width": 365, "height": 192, "relativeX": 1000, "relativeY": 0}, "fill": "paint_70:9762", "strokeColor": "paint_56:00776", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "1px", "children": [{"type": "INSTANCE", "id": "158:02652", "layoutStyle": {"width": 365, "height": 192, "relativeX": 0}, "componentInfo": {"description": "Box[shadowed=true]", "documentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Box.md"], "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Box.md"], "properties": {"Type": "Shadow"}}, "children": [{"type": "FRAME", "id": "158:02652/158:00176", "name": "slot", "layoutStyle": {"width": 325, "height": 144, "relativeX": 20, "relativeY": 24}, "flexGrow": 1, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:02653", "layoutStyle": {"width": 325, "height": 144}, "flexGrow": 1, "componentInfo": {"description": "Space[vertical=true]", "componentSetDescription": "Space组件", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Space.md"], "properties": {"Size": "Size13", "Vertical": "True"}}, "children": [{"type": "FRAME", "id": "158:02653/158:01623", "name": "slot", "layoutStyle": {"width": 325, "height": 40, "relativeY": 0}, "flexGrow": 1, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:02654", "layoutStyle": {"width": 40, "height": 40}, "componentInfo": {"description": "{icon: \"<PERSON><PERSON><PERSON><PERSON>\"}", "documentLink": ["https://adc-product-icons.io.alibaba-inc.com/llms.txt"]}, "children": [{"type": "PATH", "id": "158:02654/70:6215", "name": "路径", "layoutStyle": {"width": 16.25262451171875, "height": 10.913436889648438, "relativeX": 19.387359619140625, "relativeY": 10.69976806640625}, "path": [{"fill": "paint_2:378", "data": "M13.0021 1.74041L9.00206 4.00512L1.01874 8.73075L0 7.00966L8.01668 2.26471L12.0167 0L13.0021 1.74041Z"}]}, {"type": "PATH", "id": "158:02654/70:6217", "name": "路径", "layoutStyle": {"width": 16.25262451171875, "height": 10.913436889648438, "relativeX": 4.40826416015625, "relativeY": 10.69976806640625}, "path": [{"fill": "paint_2:378", "data": "M0.985379 0L4.98538 2.26471L13.0021 7.00966L11.9833 8.73075L4 4.00512L0 1.74041L0.985379 0Z"}]}, {"type": "PATH", "id": "158:02654/70:6219", "name": "路径", "layoutStyle": {"width": 2.5, "height": 15.700624465942383, "relativeX": 18.77410888671875, "relativeY": 20.537490844726562}, "path": [{"fill": "paint_2:378", "data": "M0 12.5605L0 0L2 0L2 12.5605L0 12.5605Z"}]}, {"type": "PATH", "id": "158:02654/70:6220", "name": "形状", "layoutStyle": {"width": 17.5, "height": 19.863500595092773, "relativeX": 11.27410888671875, "relativeY": 10.601959228515625}, "path": [{"fill": "paint_2:378", "data": "M6.99819 0L7.49026 0.276792L12.2903 2.97679L14 3.974L14 11.9272L8.7862 14.9223L6.98766 15.8908L0 11.9311L0 3.96564L6.99819 0ZM7.00181 2.29674L2 5.1311L2 10.7656L7.01234 13.606L7.81375 13.1744L12 10.7696L12 5.12268L11.3097 4.71994L7.00181 2.29674Z"}]}, {"type": "PATH", "id": "158:02654/70:6216", "name": "路径", "layoutStyle": {"width": 21.042499542236328, "height": 19.01099967956543, "relativeX": 3.77410888671875, "relativeY": 1.5997314453125}, "path": [{"fill": "paint_2:378", "data": "M0 15.2088L0 7.43053L13.0713 0L16.834 2.1379L15.846 3.87681L13.0716 2.30043L2 8.59418L2 15.2088L0 15.2088Z"}]}, {"type": "PATH", "id": "158:02654/70:6221", "name": "路径", "layoutStyle": {"width": 9.319586753845215, "height": 26.10799789428711, "relativeX": 26.906280517578125, "relativeY": 5.700836181640625}, "path": [{"fill": "paint_2:378", "data": "M0.988483 0L7.45567 3.67683L7.45567 18.5562L3.34605 20.8864L2.35958 19.1466L5.45567 17.3911L5.45567 4.84039L0 1.73865L0.988483 0Z"}]}, {"type": "PATH", "id": "158:02654/70:6218", "name": "路径", "layoutStyle": {"width": 24.223249435424805, "height": 14.112874984741211, "relativeX": 3.77410888671875, "relativeY": 24.287490844726562}, "path": [{"fill": "paint_2:378", "data": "M19.3786 7.70355L13.0715 11.2903L0 3.85968L0 0L2 0L2 2.69604L13.0713 8.98966L18.3899 5.96502L19.3786 7.70355Z"}]}], "componentId": "70:6214"}], "effect": "effect_70:5269", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "fixed"}}, {"type": "FRAME", "id": "158:02653/158:01624", "name": "slot", "layoutStyle": {"width": 325, "height": 56, "relativeY": 52}, "flexGrow": 1, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:02655", "layoutStyle": {"width": 325, "height": 56}, "flexGrow": 1, "componentInfo": {"description": "此为三级标题，使用 Typography.H3 子组件", "documentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "componentSetDescription": "标题组件，使用 Typography.Banner 或 Typography.H1 - TypographyH6", "componentSetDocumentLink": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md"], "properties": {"Type": "H3"}}, "children": [{"type": "TEXT", "id": "158:02655/9:639", "name": "主要按钮", "layoutStyle": {"width": 325, "height": 56, "relativeX": 0, "relativeY": 0}, "flexShrink": 1, "text": [{"text": "您的RDS问题得不到解答？来了解更多常见问题，找到您的答案", "font": "font_9:642"}], "textColor": [{"start": 0, "end": 29, "color": "paint_9:643"}], "textAlign": "left", "textMode": "auto-height"}], "componentId": "9:646"}], "effect": "effect_70:5269", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "fixed"}}, {"type": "FRAME", "id": "158:02653/158:01633", "name": "slot", "layoutStyle": {"width": 325, "height": 24, "relativeY": 120}, "flexGrow": 1, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "158:02660", "layoutStyle": {"width": 24, "height": 24}, "componentInfo": {"description": "{icon: \"ArrowRightOutlined\"}", "documentLink": ["https://adc-icons.io.alibaba-inc.com/llms.txt"]}, "children": [{"type": "PATH", "id": "158:02660/70:7263", "name": "联集-1", "layoutStyle": {"width": 17.935714721679688, "height": 13.060661315917969, "relativeX": 3, "relativeY": 5.469635009765625}, "effect": "effect_158:01628", "path": [{"fill": "paint_56:734", "data": "M20.0859 7.70715L13.793 1.41421L15.2072 0L23.9143 8.70711L15.2072 17.4142L13.793 16L20.0858 9.70715L0 9.70715L0 7.70715L20.0859 7.70715Z"}]}], "componentId": "70:7262"}], "effect": "effect_158:01628", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "fixed"}}], "componentId": "158:01622"}], "effect": "effect_158:5933", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "fixed"}}], "componentId": "158:00175"}], "effect": "effect_70:5269", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "auto", "crossSizing": "auto"}}], "componentId": "158:01833"}], "effect": "effect_158:5933", "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto"}}], "componentId": "158:01796"}], "components": []}, "componentDocumentLinks": ["https://unpkg.alibaba-inc.com/@ali/adc-material-docs@0.0.5-beta.1/build/llms/Container.md", "https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Flex.md", "https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Tabs.md", "https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Typography.md", "https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Box.md", "https://unpkg.alibaba-inc.com/@ali/adc-material-docs@beta/build/llms/Space.md"], "rules": ["token filed must be generated as a variable (colors, shadows, fonts, etc.) and the token field must be displayed in the comment", "\n            componentDocumentLinks is a list of frontend component documentation links used in the DSL layer, designed to help you understand how to use the components.\n            When it exists and is not empty, you need to use mcp__getComponentLink in a for loop to get the URL content of all components in the list, understand how to use the components, and generate code using the components.\n            For example: \n              ```js  \n                const componentDocumentLinks = [\n                  'https://example.com/ant/button.mdx',\n                  'https://example.com/ant/button.mdx'\n                ]\n                for (const url of componentDocumentLinks) {\n                  const componentLink = await mcp__getComponentLink(url);\n                  console.log(componentLink);\n                }\n              ```\n          "]}