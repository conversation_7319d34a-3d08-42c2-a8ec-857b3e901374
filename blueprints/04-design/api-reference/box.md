# Box 盒子

基础容器组件，参考 Chakra UI 的 Box 组件，结合阿里云 Design Tokens，快速创建通用容器。

:::info
Box 推荐与 Flex、Grid 等组件组合使用，适合各种布局场景。
:::

## API

| 参数      | 说明           | 类型                                                                                                                                                                       | 默认值 |
| --------- | -------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------ |
| bg        | 背景色         | `"default"`\|`"primary"`\|`"secondary"`\|`"success"`\|`"warning"`\|`"error"`\|`"info"`\|`"white"`\|`"transparent"`                                                        | -      |
| p         | 内边距         | `BaseSpacingType`                                                                                                                                                          | -      |
| px        | 水平内边距     | `BaseSpacingType`                                                                                                                                                          | -      |
| py        | 垂直内边距     | `BaseSpacingType`                                                                                                                                                          | -      |
| pt        | 上内边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| pb        | 下内边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| pl        | 左内边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| pr        | 右内边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| m         | 外边距         | `BaseSpacingType`                                                                                                                                                          | -      |
| mx        | 水平外边距     | `BaseSpacingType`                                                                                                                                                          | -      |
| my        | 垂直外边距     | `BaseSpacingType`                                                                                                                                                          | -      |
| mt        | 上外边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| mb        | 下外边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| ml        | 左外边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| mr        | 右外边距       | `BaseSpacingType`                                                                                                                                                          | -      |
| width     | 宽度           | `number`\|`string`                                                                                                                                                         | -      |
| height    | 高度           | `number`\|`string`                                                                                                                                                         | -      |
| bordered  | 边框           | `boolean`                                                                                                                                                                  | false  |
| shadowed  | 阴影           | `boolean`                                                                                                                                                                  | false  |
| centered  | 内容居中       | `boolean`                                                                                                                                                                  | false  |
| gap       | 子元素间隙     | `BaseSpacingType`                                                                                                                                                          | -      |
| flex      | Flex 属性      | `number`\|`string`                                                                                                                                                         | -      |
| sm        | 小屏幕下属性   | `object`                                                                                                                                                                   | -      |
| lg        | 大屏幕下属性   | `object`                                                                                                                                                                   | -      |

:::info
Box 同时支持所有原生 div 元素的属性，如 className、style 等。
:::

## 设计

- Box 组件是最基础的容器组件，类似于 HTML 的 div 标签。
- 提供快捷的样式配置能力，基于阿里云 Design Tokens。
- 支持响应式属性配置，适配不同屏幕尺寸。
- 可作为其他组件的基础容器使用。

:::info
Box 适合用于卡片、容器、布局等各种场景。
:::

## 使用示例

### 基础用法

展示 Box 组件的基本使用方式：

```jsx
/**
 * title: 基本
 * description: 基础的容器样式
 */
import { Box, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Box p="md" bg="primary" bordered shadowed>
      <Text color="white">基础容器</Text>
    </Box>
  );
};
```

### 内外边距

通过快捷属性设置内外边距：

```jsx
/**
 * title: 内外边距
 * description: 使用快捷属性设置各种边距
 */
import { Box, Space, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Space vertical gap="md">
      <Box p="lg" bg="success" bordered>
        <Text color="white">大内边距</Text>
      </Box>
      <Box px="xl" py="sm" bg="info" bordered>
        <Text color="white">水平大内边距，垂直小内边距</Text>
      </Box>
      <Box m="lg" p="md" bg="warning" bordered>
        <Text color="white">外边距 + 内边距</Text>
      </Box>
    </Space>
  );
};
```

### 响应式布局

在不同屏幕尺寸下使用不同的样式：

```jsx
/**
 * title: 响应式
 * description: 小屏幕和大屏幕下使用不同样式
 */
import { Box, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Box
      p="lg"
      bg="primary"
      bordered
      sm={{
        p: 'sm',
        bg: 'secondary',
      }}
      lg={{
        p: 'xl',
        bg: 'success',
      }}>
      <Text color="white">响应式容器</Text>
    </Box>
  );
};
```

### 卡片样式

使用 Box 创建卡片样式：

```jsx
/**
 * title: 卡片
 * description: 使用 Box 创建常见的卡片样式
 */
import { Box, Space, Typography } from '@ali/adc';

const { H4, Text } = Typography;

export default () => {
  return (
    <Space gap="lg">
      <Box bordered shadowed p="lg" width={200}>
        <Space vertical gap="sm">
          <H4>卡片标题</H4>
          <Text color="subtlest">这是卡片的描述内容</Text>
        </Space>
      </Box>
      <Box bordered shadowed p="lg" width={200} bg="primary">
        <Space vertical gap="sm">
          <H4 color="white">主色卡片</H4>
          <Text color="white">这是主色背景的卡片</Text>
        </Space>
      </Box>
    </Space>
  );
};
```

### Flex 布局配合

与 Flex 组件配合使用：

```jsx
/**
 * title: Flex 配合
 * description: Box 与 Flex 组合使用
 */
import { Box, Flex, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Flex gap="md">
      <Box flex={1} p="md" bg="success" bordered centered>
        <Text color="white">弹性容器 1</Text>
      </Box>
      <Box flex={2} p="md" bg="info" bordered centered>
        <Text color="white">弹性容器 2</Text>
      </Box>
      <Box width={100} p="md" bg="warning" bordered centered>
        <Text color="white">固定宽度</Text>
      </Box>
    </Flex>
  );
};
``` 