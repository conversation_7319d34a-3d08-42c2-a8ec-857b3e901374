# Typography 字体

- 支持紧凑模式、响应式字体大小。
- 行高与底部间距可通过 compact、bottomSpacing、appressed 属性统一设置。

:::info
Typography 适合用于正文、标题、说明、标签等多种文本场景。
:::

## 行高与底部间距

字体在不同场景可以有对于行高和底部边距会有不同的要求，可以统一通过如下属性：

- `compact`: 紧凑模式, 为 `true` 则移除行高(`line-height: 0`)
- `bottomSpacing`: 底部外边距(即：marginBottom)，设计规范内置了三种尺寸：`sm`、`md`、`lg`。
- `appressed`: 压缩模式，移除上下外边距空间，bottomSpacing 会强制失效

:::info{title="提示"}
Typography 的子组件中， `H1`-`H6` 以及 `P` 会默认带设置 `bottomSpacing="md"`
:::

## API

### Typography.Text

正文字体，提供多种样式。

| 参数      | 说明       | 类型                                                                                                                          | 默认值  |
| --------- | ---------- | ----------------------------------------------------------------------------------------------------------------------------- | ------- |
| compact   | 紧凑模式   | `boolean`                                                                                                                     | false   |
| code      | 代码样式   | `boolean`                                                                                                                     | false   |
| underline | 下划线样式 | `boolean`                                                                                                                     | false   |
| delete    | 删除线样式 | `boolean`                                                                                                                     | false   |
| strong    | 加粗样式   | `boolean`                                                                                                                     | false   |
| italic    | 斜体样式   | `boolean`                                                                                                                     | false   |
| disabled  | 禁用样式   | `boolean`                                                                                                                     | false   |
| color     | 字体颜色   | `"default"`\|`"subtle"`\|`"subtler"`\|`"subtlest"`\|`"primary"`\|`"secondary"`\|`"success"`\|`"warning"`\|`"error"`\|`"info"` | default |

### Typography.Banner

频道 Banner 类通栏标题。

| 参数          | 说明            | 类型                   | 默认值 |
| ------------- | --------------- | ---------------------- | ------ |
| compact       | 紧凑模式        | `boolean`              | false  |
| appressed     | 移除垂直 margin | `boolean`              | false  |
| bottomSpacing | 底部边距        | `"sm"`\|`"md"`\|`"lg"` | md     |

### Typography.H1 ~ Typography.H6

指定层级标题，默认输出为 h\* 标签。

| 参数      | 说明            | 类型                   | 默认值 |
| --------- | --------------- | ---------------------- | ------ |
| compact   | 紧凑模式        | `boolean`              | false  |
| appressed | 移除垂直 margin | `boolean`              | false  |
| mb        | 底部边距        | `"sm"`\|`"md"`\|`"lg"` | md     |

:::info
H1 和 Banner 实际渲染为 <h1>，建议单页仅出现一次。
:::

各类型尺寸，具体尺寸和行高等内容，请查看字体尺寸表。

### Typography.Caption

辅助、说明类字体。

### Typography.Link

字体类链接，配置同 Typography.Text。

| 参数   | 说明        | 类型                                         | 默认值  |
| ------ | ----------- | -------------------------------------------- | ------- |
| href   | 链接地址    | `string`                                     | -       |
| target | 链接 target | `"_blank"`\|`"_self"`\|`"_parent"`\|`"_top"` | \_blank |

### Typography.P

段落，支持内容间距和底部边距。

| 参数          | 说明     | 类型                            | 默认值 |
| ------------- | -------- | ------------------------------- | ------ |
| space         | 内容间距 | `"sm"`\|`"md"`\|`"lg"`          | md     |
| align         | 对齐方式 | `"left"`\|`"center"`\|`"right"` | left   |
| bottomSpacing | 底部边距 | `"sm"`\|`"md"`\|`"lg"`          | md     |
| appressed     | 压缩模式 | `boolean`                       | false  |

:::info
Typography 组件适合正文、标题、说明、链接等多种文本场景。
:::

```jsx | pure
import { Typography } from '@ali/adc';

const { P, Text } = Tyopgraphy;

export default () => {
  return (
    <>
      <P>文本</P>
      {/* 等同于 */}
      <P>
        <Text>文本</Text>
      </P>
    </>
  );
};
```

## 设计

- Typography 组件支持多种字体类型、层级和间距，适配不同场景。
- 小屏幕下自动缩减字体大小，适合响应式页面。
- 频道标题和一级标题实际渲染为 <h1>，建议单页仅出现一次。

### 字体设计规范

| 类型     | 元素 | 尺寸 | 行高 | 字重 | 字间距 | 移动端-尺寸 | 移动端-行高 | 移动端字重 | 移动端-字间距 |
| -------- | ---- | ---- | ---- | ---- | ------ | ----------- | ----------- | ---------- | ------------- |
| 频道标题 | h1   | 44px | 64px | 600  | 0      | 26px        | 42px        | 600        | 0             |
| 一级标题 | h1   | 34px | 44px | 600  | 0.25   | 24px        | 37px        | 600        | 0             |
| 二级标题 | h2   | 26px | 40px | 600  | 0      | 20px        | 30px        | 600        | 0             |
| 三级标题 | h3   | 18px | 28px | 600  | 0.15   | 16px        | 24px        | 600        | 0             |
| 四级标题 | h4   | 16px | 24px | 600  | 0.5    | 16px        | 24px        | 600        | 0             |
| 五级标题 | h5   | 15px | 24px | 600  | 0.4    | 15px        | 24px        | 600        | 0.5           |
| 六级标题 | h6   | 14px | 24px | 600  | 0.4    | 16px        | 24px        | 600        | 0.4           |
| 正文     | span | 14px | 24px | 400  | 0.4    | 14px        | 24px        | 400        | 0.4           |
| 辅助文本 | span | 12px | 20px | 400  | 0.4    | 12px        | 20px        | 400        | 0.4           |

### 间距规范

每种字体支持大、中、小三种间距，适配不同场景。

| 字体·字号 | 频道 | H1  | H2  | H3  | H4  | H5  | H6  | P   |
| --------- | ---- | --- | --- | --- | --- | --- | --- | --- |
| 大间距    | 56   | 48  | 40  | 32  | 28  | 26  | 24  | 24  |
| 中间距    | 36   | 28  | 24  | 14  | 12  | 10  | 8   | 8   |
| 小间距    | 12   | 12  | 10  | 8   | 6   | 6   | 4   | 4   |

## 使用示例

### 基础用法

展示 Typography 的基本使用方式：

```jsx
/**
 * title: 基本
 * description: 基础的文本样式
 */
import { Space, Typography } from '@ali/adc';

const { Text, P, Link } = Typography;

export default () => {
  return (
    <Space vertical gap="xs">
      <Text>普通文本</Text>
      <Text underline>下划线</Text>
      <Text delete>删除线</Text>
      <Text code>code</Text>
      <Text disabled>禁用</Text>
      <Text italic>斜体</Text>
      <Text strong>加粗</Text>
      <Text compact>紧凑模式(移除行高)</Text>
      <P>
        <Link href="https://www.aliyun.com" target="_blank">
          文本链接
        </Link>
        <Link href="https://www.aliyun.com" target="_blank" disabled>
          文本链接(禁用)
        </Link>
      </P>
    </Space>
  );
};
```

### 颜色样式

通过 `color` 属性设置文本的颜色：

```jsx
/**
 * title: 字体颜色
 * description: 基于 Design Tokens 设计，通过 `color` 属性配置内置字体色
 */
import { Box, Space, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Space vertical>
      <Text color="primary">主品牌色</Text>
      <Text color="secondary">副品牌色</Text>
      <Text color="success">成功</Text>
      <Text color="info">信息</Text>
      <Text color="warning">警告</Text>
      <Text color="error">错误</Text>

      <Box bg="primary">
        <Text color="white">白色</Text>
      </Box>
    </Space>
  );
};
```

### 标题层级

通过 `level` 属性设置标题的层级：

```jsx
/**
 * title: 字体层级
 * description: 直接使用 `Typography.H1` ~ `Typography.H6` 快速调用各级标题。支持移除行高，增加底部边距。
 * cols: 2
 */
import { useState } from 'react';
import { Box, Tag, Typography } from '@ali/adc';

const { Banner, H1, H2, H3, H4, H5, H6, Text, Caption, P } = Typography;

const tagProps = {
  bordered: true,
  className: 'cursor-pointer',
  size: 'sm',
};

export default () => {
  // 紧凑模式，移除行高
  const [compact, setCompact] = useState(false);
  // 移除上下外边距
  const [appressed, setAppressed] = useState(false);
  // 设置底部边距尺寸
  const [bottomSpacing, setBottomSpacing] = useState('md');

  return (
    <Box gap="md">
      <P>
        紧凑模式:
        <Tag
          size="md"
          color="white"
          type="outlined"
          bordered
          className="cursor-pointer"
          onClick={() => setCompact(!compact)}>
          点击切换({compact ? '紧凑' : '正常'})
        </Tag>
      </P>
      <P>
        压缩边距:
        <Tag
          size="md"
          color="white"
          type="outlined"
          bordered
          className="cursor-pointer"
          onClick={() => setAppressed(!appressed)}>
          点击切换({appressed ? '压缩' : '正常'})
        </Tag>
      </P>

      <Box>
        <Banner compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          频道 Banner 标题
        </Banner>
        <H1 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          一级标题
        </H1>
        <H2 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          二级标题
        </H2>
        <H3 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          三级标题
        </H3>
        <H4 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          四级标题
        </H4>
        <H5 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          五级标题
        </H5>
        <H6 compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
          六级标题
        </H6>
        <P bottomSpacing={bottomSpacing} appressed={appressed}>
          <Text compact={compact} bottomSpacing={bottomSpacing} appressed={appressed}>
            正文文本
          </Text>
          /<Caption compact={compact}>标签/说明/备注</Caption>
        </P>
      </Box>
    </Box>
  );
};
```

### 段落样式

展示段落文本的基本样式：

```jsx
/**
 * title: 段落
 * description: 多组内容自动填充
 * cols: 2
 */
import { useState } from 'react';
import { Tag, Typography } from '@ali/adc';

const { P, Link } = Typography;

export default () => {
  const [align, setAlign] = useState('left');
  const [space, setSpace] = useState('md');

  const alignOptions = ['left', 'center', 'right'];
  const spaceOptions = ['sm', 'md', 'lg'];

  return (
    <>
      <P>
        对齐方式:
        {alignOptions.map((it) => (
          <Tag
            key={it}
            bordered
            size="md"
            className="cursor-pointer"
            type={align === it ? 'solid' : 'outlined'}
            color={align === it ? 'primary' : 'white'}
            onClick={() => setAlign(it)}>
            {it}
          </Tag>
        ))}
      </P>

      <P>
        间距尺寸:
        {spaceOptions.map((it) => (
          <Tag
            key={it}
            bordered
            size="md"
            className="cursor-pointer"
            type={space === it ? 'solid' : 'outlined'}
            color={space === it ? 'primary' : 'white'}
            onClick={() => setSpace(it)}>
            {it}
          </Tag>
        ))}
      </P>

      <P align={align} space={space}>
        我把西西弗留在山脚下！
        <Link href="https://www.aliyun.com">立即查看</Link>
      </P>
    </>
  );
};
```

### 文字颜色分级

通过 `textColor` 属性设置文字的颜色：

```jsx
/**
 * title: 颜色分级
 * description: 字体颜色
 */
import { Space, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Space vertical>
      <Text>正文文本</Text>
      <Text color="subtle">次级文本</Text>
      <Text color="subtler">三级文本</Text>
      <Text color="subtlest">四级文本</Text>
      <Text disabled>禁用文本</Text>
    </Space>
  );
};
``` 