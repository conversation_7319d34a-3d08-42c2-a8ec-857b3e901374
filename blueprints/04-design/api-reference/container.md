# Container 容器

基础布局容器，常用于页面主内容区的分层、分块。

- 小于 1056px：宽度 100%，水平内边距 16px
- 大于等于 1056px：最大宽度 1920px，水平内边距 24px

:::info
Container 推荐与 Box、Flex 等组件组合使用。
:::

## 响应式规则

- 小于 1056px：宽度 100%，水平内边距 16px
- 大于等于 1056px：最大宽度 1920px，水平内边距 24px

:::info
Container 适合用作页面主内容区的包裹容器。
:::

## 设计

- Container 用于楼层类容器展示，支持内置断点的响应式布局。
- 通常与 Box 或 Flex 组件组合使用，实现更灵活的布局。

:::info
Container 适合页面主内容区的分块、分层包裹。
:::

## 使用示例

### 基础用法

展示容器的基本使用方式：

```jsx
import { Container, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => (
  <Container centered height={100} bg="primary">
    <Text color="white">内容区</Text>
  </Container>
);
``` 