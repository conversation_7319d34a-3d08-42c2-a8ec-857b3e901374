# Tabs

自由定制的标签选项，支持多种类型和尺寸。

### 特性

- 细分组件：List、Tab、Panels、Panel，便于精细化控制
- 自动受控处理：选中值支持自动受控管理
- 多种类型选择：支持多种标签页布局
- SEO 友好：设计时考虑搜索引擎优化

:::info
Tabs 适合用于内容分组、切换、表单分步等场景。
:::

### 待优化功能：

- [ ] 实现 Tabs 的进入动画

## API

### Tabs

标签页容器，支持响应式属性 `sm`、`lg` 定制不同样式。

| 参数         | 说明             | 类型                                                                          | 默认值    |
| ------------ | ---------------- | ----------------------------------------------------------------------------- | --------- |
| value        | 选中的选项卡     | `string`\|`number`                                                            | -         |
| defaultValue | 默认选中的选项卡 | `string`\|`number`                                                            | -         |
| size         | 尺寸             | `"sm"`\|`"md"`\|`"lg"`                                                        | md        |
| type         | 类型             | `"underline"`\|`"overline"`\|`"divider"`\|`"block"`                           | underline |
| full         | 是否均分父容器   | `boolean`                                                                     | false     |
| sm           | 小屏幕下属性     | `{size?:TabsProps['size'], type?:TabsProps['type'], full?:TabsProps['full']}` | -         |
| lg           | 大屏幕下属性     | `{size?:TabsProps['size'], type?:TabsProps['type'], full?:TabsProps['full']}` | -         |
| onChange     | 切换回调         | `(value:string\|number)=>void`                                                | -         |

:::info
full=true 仅适用于标签数量和文本长度确定的情况，过多可能导致布局错乱。
:::

### Tabs.List

标签页标签列表

| 参数     | 说明         | 类型                 | 默认值 |
| -------- | ------------ | -------------------- | ------ |
| align    | 对齐方式     | `"left"`\|`"center"` | left   |
| bordered | 是否有下划线 | `boolean`            | -      |

### Tabs.Tab

标签页标签

| 参数     | 说明       | 类型               | 默认值 |
| -------- | ---------- | ------------------ | ------ |
| disabled | 是否禁用   | `boolean`          | false  |
| value    | 标签标记值 | `string`\|`number` | -      |

### Tabs.Panels

标签页面板列表，可设置内容区域统一样式 `className`。

:::info
Tabs.Panels 内边距需自定义，可结合 Box 组件属性添加 px、py 等。
:::

### Tabs.Panel

标签页面板

| 参数  | 说明       | 类型               | 默认值 |
| ----- | ---------- | ------------------ | ------ |
| value | 标签标记值 | `string`\|`number` | -      |

> Tabs.Panel 的值需与 Tabs.Tab 对应，组件以此对应关系激活面板。

## 设计

- Tabs 支持基础、卡片式、文本式等多种类型，适合内容切换、分组场景。
- 可嵌套使用，支持多级标签页。

### 尺寸标注

- 提供多种尺寸，适配不同层级和布局需求。

:::info
Tabs 适合用于页面分组、内容切换、表单分步等场景。
:::

### 选项卡类型

#### 1. 基础选项卡

可用于容器顶部，也可用于容器内部，是最通用的 Tabs。

#### 2. 卡片式选项卡

始终与附加的背景容器配对的强调选项卡，常用于容器顶部，通常用于较大的内容区域，例如子页面。

#### 3. 文本式选项卡

通常应用于轻量内容的切换，也可作为更次级的页签来使用。

### 层级原则

不同类型的选项卡可以进行嵌套使用，在多个级别使用时，我们在规范中提供了三级选项卡。

## 使用示例

### 基础用法

展示基本的标签页样式和用法：

```jsx
/**
 * title: 基本
 * description: 简单场景可不设置value， 但需保证 `Tabs.List` 和 `Tabs.Panels` 子元素数量一致
 */
import { Tabs, Typography } from '@ali/adc';

const { P } = Typography;

export default () => {
  return (
    <div>
      <Tabs>
        <Tabs.List align="left">
          <Tabs.Tab>选项一</Tabs.Tab>
          <Tabs.Tab>选项二</Tabs.Tab>
          <Tabs.Tab>选项三</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panels>
          <Tabs.Panel>
            <P>内容1</P>
          </Tabs.Panel>
          <Tabs.Panel>
            <P>内容2</P>
          </Tabs.Panel>
          <Tabs.Panel>
            <P>内容3</P>
          </Tabs.Panel>
        </Tabs.Panels>
      </Tabs>
    </div>
  );
};
```

### 受控组件

通过编程控制选项卡的切换：

```jsx
/**
 * title: 受控模式
 * description: 通过 `value` 和 `onTabChange` 来切换 Tab 选项
 */
import { useState } from 'react';
import { Button, Tabs, Typography } from '@ali/adc';

const { P } = Typography;

export default () => {
  const [value, setValue] = useState('tab1');

  return (
    <Tabs value={value} onChange={(v) => setValue(v)}>
      <Tabs.List align="left">
        <Tabs.Tab value="tab1">选项一</Tabs.Tab>
        <Tabs.Tab value="tab2">选项二</Tabs.Tab>
        <Tabs.Tab value="tab3">选项三</Tabs.Tab>
      </Tabs.List>

      <Tabs.Panels>
        <Tabs.Panel value="tab1">
          <P>内容1</P>
          <P>
            切换到:
            <Button size="sm" type="outlined" onClick={() => setValue('tab3')}>
              选项三
            </Button>
          </P>
        </Tabs.Panel>
        <Tabs.Panel value="tab2">
          <P>内容2</P>
        </Tabs.Panel>
        <Tabs.Panel value="tab3">
          <P>内容3</P>
        </Tabs.Panel>
      </Tabs.Panels>
    </Tabs>
  );
};
```

### 响应式布局

在不同屏幕尺寸下自动调整选项卡布局：

```jsx
/**
 * title: 响应式
 * description: 不同屏幕下使用不同的类型
 */
import { Tabs } from '@ali/adc';

export default () => {
  return (
    <Tabs sm={{ type: 'block', full: true }}>
      <Tabs.List>
        <Tabs.Tab>选项一</Tabs.Tab>
        <Tabs.Tab>选项二</Tabs.Tab>
        <Tabs.Tab>选项三</Tabs.Tab>
      </Tabs.List>

      <Tabs.Panels sm={{ px: 0, pt: 4 }}>
        <Tabs.Panel>内容一</Tabs.Panel>
        <Tabs.Panel>内容二</Tabs.Panel>
        <Tabs.Panel>内容三</Tabs.Panel>
      </Tabs.Panels>
    </Tabs>
  );
};
```

### 可滚动选项卡

当选项卡过多时，支持左右滚动：

```jsx
/**
 * title: 滚动
 * description: 超出自动滚动和定位
 */
import { Tabs, Typography } from '@ali/adc';

const { P } = Typography;

const tabList = new Array(20).fill(undefined).map((v, i) => `选项${i + 1}`);

export default () => {
  return (
    <div>
      <Tabs>
        <Tabs.List align="left">
          {tabList.map((tab) => (
            <Tabs.Tab key={tab}>{tab}</Tabs.Tab>
          ))}
        </Tabs.List>

        <Tabs.Panels>
          {tabList.map((tab) => (
            <Tabs.Panel key={tab}>
              <P>{tab}-内容</P>
            </Tabs.Panel>
          ))}
        </Tabs.Panels>
      </Tabs>
    </div>
  );
};
```

### 尺寸和类型

支持多种尺寸和类型：

```jsx
/**
 * title: 尺寸和类型
 * description: 支持多种尺寸和类型
 */
import { useState } from 'react';
import { Button, Tabs, Typography } from '@ali/adc';

const { P, Text } = Typography;

export default () => {
  const [size, setSize] = useState('md');
  const [type, setType] = useState('underline');
  const [align, setAlign] = useState('left');
  const [full, setFull] = useState(false);

  return (
    <div>
      <P>
        <Text strong>修改尺寸:</Text>
        <Button size="sm" type={size === 'sm' ? 'solid' : 'default'} onClick={() => setSize('sm')}>
          小
        </Button>
        <Button size="sm" type={size === 'md' ? 'solid' : 'default'} onClick={() => setSize('md')}>
          中
        </Button>
        <Button size="sm" type={size === 'lg' ? 'solid' : 'default'} onClick={() => setSize('lg')}>
          大
        </Button>
        <Button size="sm" type={size === 'xl' ? 'solid' : 'default'} onClick={() => setSize('xl')}>
          超大
        </Button>
      </P>

      <P>
        <Text strong>修改类型:</Text>
        <Button
          size="sm"
          type={type === 'underline' ? 'solid' : 'default'}
          onClick={() => setType('underline')}>
          underline
        </Button>
        <Button
          size="sm"
          type={type === 'overline' ? 'solid' : 'default'}
          onClick={() => setType('overline')}>
          overline
        </Button>
        <Button
          size="sm"
          type={type === 'divider' ? 'solid' : 'default'}
          onClick={() => setType('divider')}>
          divider
        </Button>
        <Button
          size="sm"
          type={type === 'block' ? 'solid' : 'default'}
          onClick={() => setType('block')}>
          block
        </Button>
      </P>

      <Tabs full={full} size={size} type={type}>
        <Tabs.List align={align}>
          <Tabs.Tab>选项一</Tabs.Tab>
          <Tabs.Tab>选项二</Tabs.Tab>
          <Tabs.Tab>选项三</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panels>
          <Tabs.Panel>
            <P>内容1</P>
          </Tabs.Panel>
          <Tabs.Panel>
            <P>内容2</P>
          </Tabs.Panel>
          <Tabs.Panel>
            <P>内容3</P>
          </Tabs.Panel>
        </Tabs.Panels>
      </Tabs>
    </div>
  );
};
``` 