# COM Design 组件文档索引

本文档包含从 api-reference.md 中提取的所有 COM Design 组件的完整使用文档。每个组件都有独立的文档文件，包含详细的 API 说明、设计理念和使用示例。

## 组件总览

| 组件英文名 | 中文含义 | 详细文档路径 |
|-----------|----------|-------------|
| Container | 容器 | `03-design/api-reference/container.md` |
| Flex | 弹性布局 | `03-design/api-reference/flex.md` |
| Tabs | 标签页 | `03-design/api-reference/tabs.md` |
| Typography | 字体 | `03-design/api-reference/typography.md` |
| Box | 盒子 | `03-design/api-reference/box.md` |
| Space | 间距 | `03-design/api-reference/space.md` |

## 组件列表

### 1. [Container 容器](./container.md)

**文件路径**: `03-design/api-reference/container.md`

基础布局容器，常用于页面主内容区的分层、分块。支持响应式布局，小于 1056px 时宽度 100%，大于等于 1056px 时最大宽度 1920px。

**主要特性**:
- 响应式布局容器
- 支持内置断点
- 推荐与 Box、Flex 等组件组合使用

---

### 2. [Flex](./flex.md)

**文件路径**: `03-design/api-reference/flex.md`

基础 Flex 容器，快速实现弹性布局。基于 CSS Flex 布局，支持垂直布局、对齐方式、换行等属性。

**主要特性**:
- 基于 CSS Flex 布局
- 支持响应式属性配置
- 继承 Box 容器所有基础属性
- 适合弹性布局、栅格、分栏等场景

**核心 API**:
- `vertical`: 垂直布局
- `justify`: 水平对齐方式
- `align`: 垂直对齐方式
- `gap`: 元素间间隙

---

### 3. [Tabs](./tabs.md)

**文件路径**: `03-design/api-reference/tabs.md`

自由定制的标签选项，支持多种类型和尺寸。提供细分组件（List、Tab、Panels、Panel）便于精细化控制。

**主要特性**:
- 细分组件便于精细化控制
- 自动受控处理
- 多种类型选择（underline、overline、divider、block）
- SEO 友好设计

**子组件**:
- `Tabs.List`: 标签页标签列表
- `Tabs.Tab`: 标签页标签
- `Tabs.Panels`: 标签页面板列表
- `Tabs.Panel`: 标签页面板

---

### 4. [Typography 字体](./typography.md)

**文件路径**: `03-design/api-reference/typography.md`

支持紧凑模式、响应式字体大小的字体组件。行高与底部间距可通过 compact、bottomSpacing、appressed 属性统一设置。

**主要特性**:
- 支持紧凑模式和响应式字体大小
- 多种字体类型和层级
- 小屏幕下自动缩减字体大小
- 完整的字体设计规范

**子组件**:
- `Typography.Text`: 正文字体
- `Typography.Banner`: 频道 Banner 标题
- `Typography.H1` ~ `Typography.H6`: 各级标题
- `Typography.P`: 段落
- `Typography.Link`: 字体类链接
- `Typography.Caption`: 辅助说明类字体

---

### 5. [Box 盒子](./box.md)

**文件路径**: `03-design/api-reference/box.md`

基础容器组件，参考 Chakra UI 的 Box 组件，结合阿里云 Design Tokens，快速创建通用容器。

**主要特性**:
- 快捷配置 margin、padding、border、shadow 等属性
- 嵌套布局，可作为 Flex、Grid 子节点
- 响应式支持 sm、lg 屏幕尺寸配置
- 类 Tailwind CSS 样式写法

**核心功能**:
- 内外边距快捷设置
- 背景色和阴影色预设
- Flex 相关属性支持
- 响应式属性覆盖

---

### 6. [Space](./space.md)

**文件路径**: `03-design/api-reference/space.md`

为行内元素自动增加间距，默认 8px。继承 Flex 容器属性，推荐与 Flex 组件组合使用。

**主要特性**:
- 行内元素自动增加间距
- 继承 Flex 容器属性
- 支持自定义分隔符
- 适合按钮组、标签组等场景

**核心 API**:
- `size`: 间隙大小
- `split`: 间隙分隔符
- `align`: 垂直对齐位置

---

## 使用指南

### 组件组合推荐

1. **页面布局**: `Container` + `Flex` + `Box`
2. **内容展示**: `Typography` + `Space`
3. **交互切换**: `Tabs` + `Box`
4. **响应式设计**: 所有组件都支持 `sm`、`lg` 响应式属性

### 设计原则

- 所有组件基于阿里云 Design Tokens 设计
- 支持响应式布局和多屏幕适配
- 提供原子化的样式配置能力
- 组件间可以灵活组合使用

### 开发建议

1. 优先使用 `Box` 替代原生 `div` 标签
2. 使用 `Flex` 实现弹性布局
3. 使用 `Space` 处理元素间距
4. 使用 `Typography` 统一文本样式
5. 合理使用响应式属性适配不同屏幕

---

## 更新日志

- **2024-12**: 从 api-reference.md 提取组件文档，创建独立文档文件
- 包含完整的 API 文档、设计理念和使用示例
- 支持响应式设计和组件组合使用 
