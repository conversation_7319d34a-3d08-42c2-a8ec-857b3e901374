# Flex

基础 Flex 容器，快速实现弹性布局。

:::info
Flex 推荐与 Box 组件组合使用，适合各种弹性布局场景。
:::

## API

| 参数         | 说明           | 类型                                                                                                       | 默认值 |
| ------------ | -------------- | ---------------------------------------------------------------------------------------------------------- | ------ |
| vertical     | 垂直布局       | `boolean`                                                                                                  | false  |
| justify      | 水平对齐方式   | `"start"`\|`"end"`\|`"center"`\|`"between"`\|`"around"`                                                    | start  |
| align        | 垂直对齐方式   | `"start"`\|`"end"`\|`"center"`\|`"baseline"`\|`"stretch"`                                                  | start  |
| alignContent | 多轴线对齐方式 | `"normal"`\|`"start"`\|`"end"`\|`"center"`\|`"between"`\|`"around"`\|`"evenly"`\|`"stretch"`\|`"baseline"` | -      |
| wrap         | 换行方式       | `"nowrap"`\|`"wrap"`\|`"wrap-reverse"`                                                                     | nowrap |
| gap          | 元素间间隙     | `BaseSpacingType`                                                                                          | -      |
| sm           | 小屏幕下属性   | `object`                                                                                                   | -      |
| lg           | 大屏幕下属性   | `object`                                                                                                   | -      |

:::info
Flex 同时继承 Box 容器所有基础属性，可嵌套使用。
:::

## 设计

- Flex 组件基于 CSS Flex 布局。
- 常用于横向、纵向响应式布局，推荐与 Box 组件组合使用。

:::info
Flex 适合用于弹性布局、栅格、分栏等场景。
:::

## 使用示例

### 基础用法

展示 Flex 布局的基本使用方式：

```jsx
/**
 * title: 基本
 * description: 简单的 Flex 容器, 支持 css-flex 的常见布局
 */
import { Box, Flex, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Flex justify="center" gap="md" alignContent="between">
      <Box flex={1} height={120} bordered shadowed centered bg="success">
        <Text color="success">成功</Text>
      </Box>
      <Box flex={1} height={120} bordered shadowed centered bg="info">
        <Text color="info">信息</Text>
      </Box>
      <Box flex={1} height={120} bordered shadowed centered bg="warning">
        <Text color="warning">警告</Text>
      </Box>
      <Box flex={1} height={120} bordered shadowed centered bg="error">
        <Text color="error">错误</Text>
      </Box>
    </Flex>
  );
};
```

### 对齐方式

通过 `align` 属性控制子元素的垂直对齐方式：

```jsx
/**
 * title: 多轴对齐
 * description: 通过 alignContent 属性，实现多轴对齐
 */
import { Box, Flex } from '@ali/adc';

export default () => {
  return (
    <Flex
      gap="sm"
      wrap="wrap"
      justify="center"
      align="center"
      alignContent="between"
      bg="default"
      height={600}>
      <Box width={100} height={100} bg="primary" />
      <Box width={400} height={300} bg="primary" />
      <Box width={100} height={200} bg="primary" />
      <Box width={500} height={100} bg="primary" />
      <Box width={100} height={100} bg="primary" />
      <Box width={400} height={100} bg="primary" />
    </Flex>
  );
};
```

### 响应式布局

在不同屏幕尺寸下动态调整 Flex 子项的排列方式：

```jsx
/**
 * title: 响应式布局
 * description: 根据屏幕尺寸，采用不同的布局方式
 */
import { Flex, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Flex
      gap="md"
      sm={{
        vertical: true,
        gap: 'sm',
      }}
      lg={{
        gap: 'lg',
      }}>
      <Text>目的地:</Text>
      <Text>A. 北京</Text>
      <Text>B. 南京</Text>
      <Text>C. 哈尔滨</Text>
      <Text>D. 西安</Text>
    </Flex>
  );
};
```

### 宽度控制

通过 `width` 属性设置 Flex 子项的宽度：

```jsx
/**
 * title: 宽度
 * description: 通过设置固定宽度和 flex 值来调整子元素宽度
 */
import { Box, Flex, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <>
      <Flex gap="lg" className="twine">
        <Box width={200} p="sm" bg="primary" centered>
          <Typography.Text color="white">200px</Typography.Text>
        </Box>
        <Box flex={1} p="sm" bg="primary" centered>
          <Typography.Text color="white">auto</Typography.Text>
        </Box>
      </Flex>

      <br />

      <Flex gap="lg" className="twine">
        <Box flex={1} p="sm" bg="primary" centered>
          <Text color="white">1</Text>
        </Box>
        <Box flex={1} p="sm" bg="primary" centered>
          <Text color="white">1</Text>
        </Box>
        <Box flex={1} p="sm" bg="primary" centered>
          <Text color="white">1</Text>
        </Box>
      </Flex>

      <br />

      <Flex gap="lg" className="twine">
        <Box flex={1} p="sm" bg="primary" centered>
          <Text color="white">1</Text>
        </Box>
        <Box flex={4} p="sm" bg="primary" centered>
          <Text color="white">4</Text>
        </Box>
      </Flex>
    </>
  );
};
``` 