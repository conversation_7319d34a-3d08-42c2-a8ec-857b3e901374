# 项目介绍

## 目录结构

严格遵循如下目录结构:

```
├── src/                      # 源代码目录
|   ├── components            # 子组件目录
|   |  └── CustomCard         # 子组件文件夹名称
|   |     ├── index.module.css
|   |     └── index.tsx       # 组件入口文件，统一使用 index.*
│   ├── hooks/                # 自定义 Hooks 目录, 如有则创建
│   ├── service/              # 服务层目录，如有则创建
|   ├── types                 # TypeScript 类型声明定义
|   |  └── index.ts
|   ├── index.module.css      # 全局样式
│   └── index.tsx             # 主入口文件,只能导出一个默认函数
├── meta/
|  ├── mock.json              # 元数据目录
|  ├── schema.json            # 数据结构描述文件
|  └── snippets.json          # 默认数据
├── Changelog.md              # 变更日志
└── README.md                 # 项目说明文件
```

## 文件命名规范

1. 组件文件：对于一个文件夹里的根组件，应该用 `index.tsx` 作为入口名，同时用文件夹名作为组件名使用 `PascalCase` 命名法
2. 工具文件：使用 `kebab case` 命名法，例如 `utils.ts`
3. 样式文件：对于一个文件夹里的样式文件，采用 `index.module.css` 或 `index.module.less`, 新创建样式文件必须使用 CSS Modules 模式
4. 类型文件：使用 `*.ts` 作为文件后缀
5. 常量文件：使用 `kebab case` 命名法，如 `constant.ts`
 