import React from 'react';
import { Container, Flex, Tabs, Typography, Box, Space } from '@ali/adc';
import { Ackdistro, ArrowRightOutlined } from '@ali/adc/icons';

import '@ali/adc/es/index.css';

import styles from './index.module.css';
import { IHmodProps } from './types';

/**
 * 产品详情-常见问题组件
 * 使用标签页展示分类列表，每个分类下展示常见问题的问答对列表
 * 每个问答对支持跳转链接，同时展示一个跳转类卡片
 *
 * @param props - 组件属性
 * @returns React 组件
 */
export default function Hmod(props: IHmodProps) {
  // 解构 props，设置默认值
  const { categories = [], jumpCard } = props;

  /**
   * 处理问题点击事件
   * 当问题有详情链接时，在新窗口打开链接
   *
   * @param url - 详情页链接
   */
  const handleQuestionClick = (url: string) => {
    if (url && url.trim()) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  /**
   * 处理跳转卡片点击事件
   * 当卡片有跳转链接时，在新窗口打开链接
   */
  const handleJumpCardClick = () => {
    if (jumpCard?.url && jumpCard.url.trim()) {
      window.open(jumpCard.url, '_blank', 'noopener,noreferrer');
    }
  };

  // 如果没有分类数据，显示空状态提示
  if (!categories || categories.length === 0) {
    return (
      <Container>
        <Box p="xl" textAlign="center">
          <Typography.Text color="subtlest">暂无常见问题数据</Typography.Text>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      {/* 主布局：左侧标签页，右侧跳转卡片，移动端垂直排列 */}
      <Flex gap="xl" sm={{ vertical: true, gap: 'lg' }}>
        {/* 左侧：问题分类标签页 */}
        <Box flex={1}>
          <Tabs size="lg" sm={{ type: 'block', full: true }}>
            {/* 标签页头部：显示所有分类名称 */}
            <Tabs.List>
              {categories.map((category, index) => (
                <Tabs.Tab key={index}>{category.name}</Tabs.Tab>
              ))}
            </Tabs.List>
            {/* 标签页内容：显示每个分类下的问答对列表 */}
            <Tabs.Panels px={0}>
              {categories.map((category, categoryIndex) => (
                <Tabs.Panel key={categoryIndex}>
                  <Space vertical gap="lg">
                    {/* 检查当前分类是否有问题数据 */}
                    {category.questions && category.questions.length > 0 ? (
                      // 渲染问答对列表
                      category.questions.map((qa, qaIndex) => (
                        <Box
                          key={qaIndex}
                          // 如果有详情链接，添加可点击样式
                          className={qa.detailUrl ? styles.questionItem : ''}
                          onClick={() => handleQuestionClick(qa.detailUrl)}
                          // 无障碍访问支持
                          role={qa.detailUrl ? "button" : undefined}
                          tabIndex={qa.detailUrl ? 0 : undefined}
                          onKeyDown={(e) => {
                            // 支持键盘操作
                            if (qa.detailUrl && (e.key === 'Enter' || e.key === ' ')) {
                              e.preventDefault();
                              handleQuestionClick(qa.detailUrl);
                            }
                          }}
                        >
                          {/* 问答对内容布局 */}
                          <Space vertical gap="sm">
                            <Typography.H6>Q：</Typography.H6>
                            <Typography.Text>{qa.question || '暂无问题'}</Typography.Text>
                            <Typography.H6>A：</Typography.H6>
                            <Typography.Text color="subtlest">
                              {qa.answer || '暂无回答'}
                            </Typography.Text>
                          </Space>
                        </Box>
                      ))
                    ) : (
                      // 当前分类无问题时的空状态
                      <Box p="md" textAlign="center">
                        <Typography.Text color="subtlest">该分类下暂无问题</Typography.Text>
                      </Box>
                    )}
                  </Space>
                </Tabs.Panel>
              ))}
            </Tabs.Panels>
          </Tabs>
        </Box>
        {/* 右侧：跳转卡片，仅在有卡片数据时显示 */}
        {jumpCard && (
          <Box width={300} sm={{ width: '100%' }}>
            <Box
              bordered
              shadowed
              p="lg"
              // 如果有跳转链接，添加可点击样式
              className={jumpCard.url ? styles.jumpCard : ''}
              onClick={handleJumpCardClick}
              // 无障碍访问支持
              role={jumpCard.url ? "button" : undefined}
              tabIndex={jumpCard.url ? 0 : undefined}
              onKeyDown={(e) => {
                // 支持键盘操作
                if (jumpCard.url && (e.key === 'Enter' || e.key === ' ')) {
                  e.preventDefault();
                  handleJumpCardClick();
                }
              }}
            >
              {/* 卡片内容：图标、标题、箭头 */}
              <Space vertical gap="md" align="center">
                {/* 卡片图标 */}
                <Box>
                  <Ackdistro size={48} />
                </Box>
                {/* 卡片标题 */}
                <Box>
                  <Typography.H4 textAlign="center">
                    {jumpCard.title || '了解更多'}
                  </Typography.H4>
                </Box>
                {/* 跳转箭头，仅在有链接时显示 */}
                {jumpCard.url && (
                  <Box>
                    <ArrowRightOutlined size={24} />
                  </Box>
                )}
              </Space>
            </Box>
          </Box>
        )}
      </Flex>
    </Container>
  );
}
