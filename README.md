# @ali/hmod-********************

<p>
    <img src="https://img.alicdn.com/imgextra/i2/O1CN01yGauiD1MGujDwNzVk_!!6000000001408-2-tps-1413-392.png" alt="预览图" width="600" />
</p>

产品详情-常见问题组件，使用标签页展示分类列表，每个分类下展示常见问题的问答对列表，每个问答对支持跳转链接，同时展示一个跳转类卡片。

## 使用说明

> 运营和开发人员使用注意事项

## 相关资源

- Aone 需求：https://project.aone.alibaba-inc.com/v2/project/2058340/req/65580309
- 设计稿：https://mgdone.alibaba-inc.com/file/162733819736234?page_id=M&devMode=true&layer_id=158%3A01827
- Git 仓库：**************************:hmod/********************.git

## 命令

本地调试

```bash
npm run dev
```

调试低代码配置

```bash
npm run dev:editor
```

代理调试线上页面中的组件

```bash
npm run proxy
```

## 开发相关

- ADC（aliyun-dot-com）基础组件库文档，请查看：[adc-design.alibaba-inc.com/](https://adc-design.alibaba-inc.com/)
