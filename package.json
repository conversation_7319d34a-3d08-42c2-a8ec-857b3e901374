{"name": "@ali/hmod-********************", "version": "0.0.1", "description": "产品详情-常见问题", "author": "lianmin.slm", "repository": {"type": "git", "url": "**************************:hmod/********************.git"}, "main": "src/index.tsx", "files": ["es", "lib"], "scripts": {"dev": "biu dev-editor", "dev:editor": "biu dev-editor", "proxy": "biu proxy", "build:npm": "biu build-npm", "bundle:cdn": "biu bundle-cdn", "lint": "eslint --no-error-on-unmatched-pattern --ext=.jsx,.js,.tsx,.ts src/"}, "dependencies": {"@ali/adc-design-tokens": "^0.1.13", "@ali/adc": "^0.2.11", "@ali/adc-icons": "^1.0.11"}, "devDependencies": {"@ali/aliyun-com-biu-cli": "^0.1.0-alpha.0", "@alife/eslint-plugin-quality": "^1.0.20", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@types/react": "^16.14.60", "eslint": "^8.57.0", "less": "^4.1.3", "prettier": "^3.3.0", "prettier-plugin-packagejson": "^2.5.1", "react": "^16.8.1", "typescript": "^4.9.5"}, "peerDependencies": {"react": "^16.8.1"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "spacexConfig": {"renderConfig": ["./**/*.ejs", "./*.ejs", "./.*.ejs"]}}